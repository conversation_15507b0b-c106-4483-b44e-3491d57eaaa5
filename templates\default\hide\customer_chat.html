<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>实时客服聊天</title>
  <link href="/css/layui/layui.css" rel="stylesheet">
  <style>
    :root {
      --primary-color: #667eea;
      --primary-light: #764ba2;
      --secondary-color: #f093fb;
      --secondary-light: #f5576c;
      --success-color: #4facfe;
      --success-light: #00f2fe;
      --warning-color: #fa709a;
      --warning-light: #fee140;
      --error-color: #ff6b6b;
      --text-primary: #2c3e50;
      --text-secondary: #7f8c8d;
      --text-light: #bdc3c7;
      --bg-primary: #ffffff;
      --bg-secondary: #f8f9fa;
      --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --border-radius: 16px;
      --border-radius-sm: 8px;
      --border-radius-lg: 24px;
      --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      --box-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    body {
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      color: var(--text-primary);
    }

    .chat-container {
      display: flex;
      min-height: 100vh;
      gap: 8px;
      position: relative;
      padding: 8px;
      box-sizing: border-box;
    }

    /* 左侧用户列表 */
    .user-panel {
      width: 320px;
      min-width: 280px;
      background: var(--bg-primary);
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
      backdrop-filter: blur(10px);
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      overflow: hidden;
    }

    .user-header {
      padding: 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      background: var(--bg-gradient);
      color: #fff;
    }

    .stats-row {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;
    }

    .stat-item {
      flex: 1;
      text-align: center;
      padding: 12px 8px;
      background: rgba(255, 255, 255, 0.15);
      border-radius: var(--border-radius-sm);
      backdrop-filter: blur(10px);
      transition: var(--transition);
    }

    .stat-item:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: translateY(-2px);
    }

    .stat-number {
      font-size: 20px;
      font-weight: 700;
      color: #fff;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 11px;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .user-search {
      margin-top: 10px;
    }

    .user-list {
      flex: 1;
      overflow-y: auto;
    }

    .user-item {
      padding: 16px 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.04);
      cursor: pointer;
      transition: var(--transition);
      position: relative;
      margin: 0 8px;
      border-radius: var(--border-radius-sm);
      margin-bottom: 4px;
    }

    .user-item:hover {
      background: rgba(102, 126, 234, 0.05);
      transform: translateX(4px);
    }

    .user-item.active {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      border-left: 4px solid var(--primary-color);
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
    }

    .user-info {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .user-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .user-last-message {
      font-size: 12px;
      color: #999;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .user-meta {
      text-align: right;
      font-size: 12px;
    }

    .user-time {
      color: #999;
      margin-bottom: 4px;
    }

    .unread-badge {
      background: var(--error-color);
      color: #fff;
      border-radius: 10px;
      padding: 2px 6px;
      font-size: 11px;
      min-width: 16px;
      text-align: center;
    }

    /* 右侧聊天面板 */
    .chat-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: var(--bg-primary);
      backdrop-filter: blur(10px);
      box-shadow: var(--box-shadow);
      border-radius: var(--border-radius);
      overflow: hidden;
    }

    .chat-header {
      padding: 20px 24px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
      color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
    }

    .chat-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      z-index: -1;
    }

    .chat-user-info {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .chat-actions {
      display: flex;
      gap: 10px;
    }

    .chat-messages {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .message {
      margin-bottom: 20px;
      display: flex;
      align-items: flex-start;
      animation: fadeInUp 0.3s ease-out;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .message.admin {
      flex-direction: row-reverse;
    }

    .message-avatar {
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius);
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 14px;
      font-weight: 600;
      margin: 0 12px;
      flex-shrink: 0;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .message.admin .message-avatar {
      background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
      box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
    }

    .message-content {
      max-width: 70%;
      padding: 12px 16px;
      border-radius: var(--border-radius);
      background: var(--bg-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      position: relative;
      border: 1px solid rgba(0, 0, 0, 0.05);
      word-wrap: break-word;
    }

    .message.admin .message-content {
      background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
      color: #fff;
      border: none;
      box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
    }

    .message-time {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
      text-align: center;
    }

    .message.admin .message-time {
      color: rgba(255, 255, 255, 0.8);
    }

    .chat-input {
      padding: 20px 24px;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      background: var(--bg-primary);
    }

    .quick-replies {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .quick-reply-btn {
      padding: 8px 16px;
      background: var(--bg-primary);
      border: 1px solid rgba(102, 126, 234, 0.2);
      border-radius: var(--border-radius-lg);
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      color: var(--text-primary);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .quick-reply-btn:hover {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      color: #fff;
      border-color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .input-group {
      display: flex;
      gap: 12px;
      align-items: flex-end;
    }

    .input-text {
      flex: 1;
      min-height: 44px;
      max-height: 100px;
      padding: 12px 16px;
      border: 2px solid rgba(102, 126, 234, 0.2);
      border-radius: var(--border-radius-lg);
      resize: none;
      outline: none;
      font-size: 14px;
      line-height: 1.4;
      background: var(--bg-primary);
      transition: var(--transition);
      font-family: inherit;
    }

    .input-text:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .send-btn {
      min-width: 44px;
      height: 44px;
      background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);
      color: #fff;
      border: none;
      border-radius: var(--border-radius);
      cursor: pointer;
      transition: var(--transition);
      font-weight: 600;
      font-size: 14px;
      padding: 0 16px;
      box-shadow: 0 2px 8px rgba(250, 112, 154, 0.3);
    }

    .send-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(250, 112, 154, 0.4);
    }

    .send-btn:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    /* 表情包面板样式 */
    .emoji-panel {
      position: absolute;
      bottom: 100%;
      left: 0;
      right: 0;
      background: var(--bg-primary);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow-hover);
      border: 1px solid rgba(0, 0, 0, 0.1);
      max-height: 300px;
      overflow: hidden;
      z-index: 1000;
      animation: slideUp 0.3s ease-out;
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .emoji-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      background: var(--bg-secondary);
    }

    .emoji-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .emoji-close {
      background: none;
      border: none;
      font-size: 18px;
      color: var(--text-secondary);
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: var(--transition);
    }

    .emoji-close:hover {
      background: rgba(0, 0, 0, 0.1);
      color: var(--text-primary);
    }

    .emoji-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
      gap: 4px;
      padding: 12px;
      max-height: 240px;
      overflow-y: auto;
    }

    .emoji-item {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      cursor: pointer;
      border-radius: var(--border-radius-sm);
      transition: var(--transition);
    }

    .emoji-item:hover {
      background: rgba(102, 126, 234, 0.1);
      transform: scale(1.2);
    }

    /* 输入工具样式 */
    .input-tools {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .tool-btn {
      width: 40px;
      height: 40px;
      border: none;
      border-radius: var(--border-radius-sm);
      background: rgba(102, 126, 234, 0.1);
      color: var(--text-primary);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: var(--transition);
    }

    .tool-btn:hover {
      background: rgba(102, 126, 234, 0.2);
      transform: scale(1.05);
    }

    .tool-btn:active {
      transform: scale(0.95);
    }

    /* 图片消息样式 */
    .message-image {
      max-width: 200px;
      max-height: 200px;
      border-radius: var(--border-radius-sm);
      cursor: pointer;
      transition: var(--transition);
    }

    .message-image:hover {
      transform: scale(1.02);
    }

    .image-upload-preview {
      position: relative;
      display: inline-block;
      margin: 8px 0;
    }

    .image-preview {
      max-width: 150px;
      max-height: 150px;
      border-radius: var(--border-radius-sm);
      border: 2px solid rgba(102, 126, 234, 0.2);
    }

    .image-remove {
      position: absolute;
      top: -8px;
      right: -8px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: var(--error-color);
      color: #fff;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--border-radius-sm);
      color: #fff;
      font-size: 12px;
    }

    .empty-chat {
      text-align: center;
      padding: 60px 20px;
      color: #999;
    }

    .empty-chat i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    .loading {
      text-align: center;
      padding: 20px;
      color: #999;
    }

    /* 移动端切换按钮 */
    .mobile-toggle {
      display: none;
      position: fixed;
      top: 20px;
      left: 20px;
      z-index: 1001;
      width: 44px;
      height: 44px;
      background: var(--primary-color);
      color: #fff;
      border: none;
      border-radius: var(--border-radius);
      cursor: pointer;
      box-shadow: var(--box-shadow-hover);
      transition: var(--transition);
    }

    .mobile-toggle:hover {
      transform: scale(1.05);
    }

    .mobile-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 999;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .user-panel {
        width: 280px;
        min-width: 260px;
      }

      .stats-row {
        flex-direction: column;
        gap: 8px;
      }

      .stat-item {
        padding: 8px;
      }

      .stat-number {
        font-size: 18px;
      }
    }

    @media (max-width: 992px) {
      .user-panel {
        width: 260px;
        min-width: 240px;
      }

      .user-header {
        padding: 16px;
      }

      .chat-header {
        padding: 16px 20px;
      }

      .chat-messages {
        padding: 16px;
      }

      .chat-input {
        padding: 16px 20px;
      }
    }

    @media (max-width: 768px) {
      .chat-container {
        flex-direction: column;
        height: 100vh;
        overflow: hidden;
        padding: 4px;
        gap: 4px;
      }

      .mobile-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .user-panel {
        position: fixed;
        top: 8px;
        left: -100%;
        width: 85%;
        max-width: 320px;
        height: calc(100vh - 16px);
        z-index: 1000;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow-hover);
        transition: left 0.3s ease-in-out;
      }

      .user-panel.mobile-open {
        left: 8px;
      }

      .mobile-overlay.active {
        display: block;
      }

      .chat-panel {
        width: 100%;
        height: calc(100vh - 8px);
        margin: 0;
        border-radius: var(--border-radius);
      }

      .chat-header {
        padding: 16px 20px 16px 70px;
        position: relative;
      }

      .stats-row {
        flex-direction: row;
        gap: 8px;
      }

      .stat-item {
        padding: 8px 6px;
      }

      .stat-number {
        font-size: 16px;
      }

      .stat-label {
        font-size: 10px;
      }

      .user-search input {
        font-size: 16px; /* 防止iOS缩放 */
      }

      .message-content {
        max-width: 85%;
      }

      .quick-replies {
        flex-wrap: wrap;
        gap: 6px;
      }

      .quick-reply-btn {
        padding: 6px 12px;
        font-size: 12px;
      }

      .input-text {
        font-size: 16px; /* 防止iOS缩放 */
        min-height: 40px;
      }

      .send-btn {
        min-width: 40px;
        height: 40px;
        padding: 0 12px;
      }

      .tool-btn {
        width: 36px;
        height: 36px;
        font-size: 14px;
      }
    }

    @media (max-width: 480px) {
      .user-panel {
        width: 90%;
      }

      .user-header {
        padding: 12px;
      }

      .chat-header {
        padding: 12px 16px 12px 60px;
        font-size: 14px;
      }

      .chat-messages {
        padding: 12px;
      }

      .chat-input {
        padding: 12px 16px;
      }

      .message {
        margin-bottom: 16px;
      }

      .message-avatar {
        width: 32px;
        height: 32px;
        font-size: 12px;
        margin: 0 8px;
      }

      .message-content {
        padding: 10px 12px;
        font-size: 14px;
        max-width: 90%;
      }

      .user-item {
        padding: 12px 16px;
        margin: 0 6px;
      }

      .user-name {
        font-size: 14px;
      }

      .user-last-message {
        font-size: 11px;
        max-width: 150px;
      }

      .quick-reply-btn {
        padding: 5px 10px;
        font-size: 11px;
      }

      .input-group {
        gap: 8px;
      }

      .input-tools {
        gap: 6px;
      }

      .tool-btn {
        width: 32px;
        height: 32px;
        font-size: 12px;
      }

      .send-btn {
        min-width: 36px;
        height: 36px;
        font-size: 12px;
      }

      .emoji-panel {
        max-height: 250px;
      }

      .emoji-grid {
        grid-template-columns: repeat(auto-fill, minmax(28px, 1fr));
        gap: 3px;
        padding: 8px;
      }

      .emoji-item {
        width: 28px;
        height: 28px;
        font-size: 16px;
      }
    }

    /* 横屏适配 */
    @media (max-width: 768px) and (orientation: landscape) {
      .chat-container {
        height: 100vh;
      }

      .user-panel {
        height: 100vh;
      }

      .chat-panel {
        height: 100vh;
      }

      .chat-messages {
        padding: 12px;
      }

      .chat-input {
        padding: 12px 16px;
      }
    }

    /* 超小屏幕适配 */
    @media (max-width: 360px) {
      .mobile-toggle {
        width: 40px;
        height: 40px;
        top: 16px;
        left: 16px;
      }

      .chat-header {
        padding: 10px 12px 10px 56px;
        font-size: 13px;
      }

      .stats-row {
        gap: 6px;
      }

      .stat-item {
        padding: 6px 4px;
      }

      .stat-number {
        font-size: 14px;
      }

      .stat-label {
        font-size: 9px;
      }
    }

    /* 高分辨率屏幕优化 */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      .message-avatar,
      .tool-btn,
      .send-btn,
      .quick-reply-btn {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    }

    /* 打印样式 */
    @media print {
      .user-panel,
      .chat-input,
      .mobile-toggle {
        display: none !important;
      }

      .chat-panel {
        width: 100% !important;
      }

      .chat-messages {
        background: #fff !important;
        box-shadow: none !important;
      }

      .message {
        break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <!-- 移动端切换按钮 -->
  <button class="mobile-toggle" onclick="toggleMobilePanel()">
    <i class="layui-icon layui-icon-list"></i>
  </button>

  <!-- 移动端遮罩层 -->
  <div class="mobile-overlay" onclick="closeMobilePanel()"></div>

  <div class="chat-container">
    <!-- 左侧用户列表 -->
    <div class="user-panel" id="userPanel">
      <div class="user-header">
        <div class="stats-row">
          <div class="stat-item">
            <div class="stat-number" id="onlineUsers">{+$online_users+}</div>
            <div class="stat-label">在线用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number" id="unreadCount">{+$unread_count+}</div>
            <div class="stat-label">未读消息</div>
          </div>
          <div class="stat-item">
            <div class="stat-number" id="todayMessages">{+$today_messages+}</div>
            <div class="stat-label">今日消息</div>
          </div>
        </div>
        <div class="user-search">
          <input type="text" class="layui-input" placeholder="搜索用户..." id="userSearch">
        </div>
      </div>
      <div class="user-list" id="userList">
        <div class="loading">加载中...</div>
      </div>
    </div>

    <!-- 右侧聊天面板 -->
    <div class="chat-panel">
      <div class="chat-header">
        <div class="chat-user-info" id="chatUserInfo">请选择用户开始对话</div>
        <div class="chat-actions">
          <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="refreshChat()" id="refreshBtn" style="display: none;">刷新</button>
        </div>
      </div>
      <div class="chat-messages" id="chatMessages">
        <div class="empty-chat">
          <i class="layui-icon layui-icon-dialogue"></i>
          <div>请选择用户开始对话</div>
        </div>
      </div>
      <div class="chat-input" id="chatInput" style="display: none;">
        <div class="quick-replies">
          <span class="quick-reply-btn" onclick="sendQuickReply(1)">欢迎咨询</span>
          <span class="quick-reply-btn" onclick="sendQuickReply(2)">请稍等</span>
          <span class="quick-reply-btn" onclick="sendQuickReply(3)">感谢咨询</span>
          <span class="quick-reply-btn" onclick="sendQuickReply(4)">抱歉久等</span>
          <span class="quick-reply-btn" onclick="sendQuickReply(5)">已记录</span>
        </div>
        <!-- 表情包面板 -->
        <div class="emoji-panel" id="emojiPanel" style="display: none;">
          <div class="emoji-header">
            <span class="emoji-title">选择表情</span>
            <button class="emoji-close" onclick="toggleEmojiPanel()">×</button>
          </div>
          <div class="emoji-grid">
            <span class="emoji-item" onclick="insertEmoji('😀')">😀</span>
            <span class="emoji-item" onclick="insertEmoji('😃')">😃</span>
            <span class="emoji-item" onclick="insertEmoji('😄')">😄</span>
            <span class="emoji-item" onclick="insertEmoji('😁')">😁</span>
            <span class="emoji-item" onclick="insertEmoji('😆')">😆</span>
            <span class="emoji-item" onclick="insertEmoji('😅')">😅</span>
            <span class="emoji-item" onclick="insertEmoji('😂')">😂</span>
            <span class="emoji-item" onclick="insertEmoji('🤣')">🤣</span>
            <span class="emoji-item" onclick="insertEmoji('😊')">😊</span>
            <span class="emoji-item" onclick="insertEmoji('😇')">😇</span>
            <span class="emoji-item" onclick="insertEmoji('🙂')">🙂</span>
            <span class="emoji-item" onclick="insertEmoji('🙃')">🙃</span>
            <span class="emoji-item" onclick="insertEmoji('😉')">😉</span>
            <span class="emoji-item" onclick="insertEmoji('😌')">😌</span>
            <span class="emoji-item" onclick="insertEmoji('😍')">😍</span>
            <span class="emoji-item" onclick="insertEmoji('🥰')">🥰</span>
            <span class="emoji-item" onclick="insertEmoji('😘')">😘</span>
            <span class="emoji-item" onclick="insertEmoji('😗')">😗</span>
            <span class="emoji-item" onclick="insertEmoji('😙')">😙</span>
            <span class="emoji-item" onclick="insertEmoji('😚')">😚</span>
            <span class="emoji-item" onclick="insertEmoji('😋')">😋</span>
            <span class="emoji-item" onclick="insertEmoji('😛')">😛</span>
            <span class="emoji-item" onclick="insertEmoji('😝')">😝</span>
            <span class="emoji-item" onclick="insertEmoji('😜')">😜</span>
            <span class="emoji-item" onclick="insertEmoji('🤪')">🤪</span>
            <span class="emoji-item" onclick="insertEmoji('🤨')">🤨</span>
            <span class="emoji-item" onclick="insertEmoji('🧐')">🧐</span>
            <span class="emoji-item" onclick="insertEmoji('🤓')">🤓</span>
            <span class="emoji-item" onclick="insertEmoji('😎')">😎</span>
            <span class="emoji-item" onclick="insertEmoji('🤩')">🤩</span>
            <span class="emoji-item" onclick="insertEmoji('🥳')">🥳</span>
            <span class="emoji-item" onclick="insertEmoji('😏')">😏</span>
            <span class="emoji-item" onclick="insertEmoji('😒')">😒</span>
            <span class="emoji-item" onclick="insertEmoji('😞')">😞</span>
            <span class="emoji-item" onclick="insertEmoji('😔')">😔</span>
            <span class="emoji-item" onclick="insertEmoji('😟')">😟</span>
            <span class="emoji-item" onclick="insertEmoji('😕')">😕</span>
            <span class="emoji-item" onclick="insertEmoji('🙁')">🙁</span>
            <span class="emoji-item" onclick="insertEmoji('☹️')">☹️</span>
            <span class="emoji-item" onclick="insertEmoji('😣')">😣</span>
            <span class="emoji-item" onclick="insertEmoji('😖')">😖</span>
            <span class="emoji-item" onclick="insertEmoji('😫')">😫</span>
            <span class="emoji-item" onclick="insertEmoji('😩')">😩</span>
            <span class="emoji-item" onclick="insertEmoji('🥺')">🥺</span>
            <span class="emoji-item" onclick="insertEmoji('😢')">😢</span>
            <span class="emoji-item" onclick="insertEmoji('😭')">😭</span>
            <span class="emoji-item" onclick="insertEmoji('😤')">😤</span>
            <span class="emoji-item" onclick="insertEmoji('😠')">😠</span>
            <span class="emoji-item" onclick="insertEmoji('😡')">😡</span>
            <span class="emoji-item" onclick="insertEmoji('🤬')">🤬</span>
            <span class="emoji-item" onclick="insertEmoji('🤯')">🤯</span>
            <span class="emoji-item" onclick="insertEmoji('😳')">😳</span>
            <span class="emoji-item" onclick="insertEmoji('🥵')">🥵</span>
            <span class="emoji-item" onclick="insertEmoji('🥶')">🥶</span>
            <span class="emoji-item" onclick="insertEmoji('😱')">😱</span>
            <span class="emoji-item" onclick="insertEmoji('😨')">😨</span>
            <span class="emoji-item" onclick="insertEmoji('😰')">😰</span>
            <span class="emoji-item" onclick="insertEmoji('😥')">😥</span>
            <span class="emoji-item" onclick="insertEmoji('😓')">😓</span>
            <span class="emoji-item" onclick="insertEmoji('🤗')">🤗</span>
            <span class="emoji-item" onclick="insertEmoji('🤔')">🤔</span>
            <span class="emoji-item" onclick="insertEmoji('🤭')">🤭</span>
            <span class="emoji-item" onclick="insertEmoji('🤫')">🤫</span>
            <span class="emoji-item" onclick="insertEmoji('🤥')">🤥</span>
            <span class="emoji-item" onclick="insertEmoji('😶')">😶</span>
            <span class="emoji-item" onclick="insertEmoji('😐')">😐</span>
            <span class="emoji-item" onclick="insertEmoji('😑')">😑</span>
            <span class="emoji-item" onclick="insertEmoji('😬')">😬</span>
            <span class="emoji-item" onclick="insertEmoji('🙄')">🙄</span>
            <span class="emoji-item" onclick="insertEmoji('😯')">😯</span>
            <span class="emoji-item" onclick="insertEmoji('😦')">😦</span>
            <span class="emoji-item" onclick="insertEmoji('😧')">😧</span>
            <span class="emoji-item" onclick="insertEmoji('😮')">😮</span>
            <span class="emoji-item" onclick="insertEmoji('😲')">😲</span>
            <span class="emoji-item" onclick="insertEmoji('🥱')">🥱</span>
            <span class="emoji-item" onclick="insertEmoji('😴')">😴</span>
            <span class="emoji-item" onclick="insertEmoji('🤤')">🤤</span>
            <span class="emoji-item" onclick="insertEmoji('😪')">😪</span>
            <span class="emoji-item" onclick="insertEmoji('😵')">😵</span>
            <span class="emoji-item" onclick="insertEmoji('🤐')">🤐</span>
            <span class="emoji-item" onclick="insertEmoji('🥴')">🥴</span>
            <span class="emoji-item" onclick="insertEmoji('🤢')">🤢</span>
            <span class="emoji-item" onclick="insertEmoji('🤮')">🤮</span>
            <span class="emoji-item" onclick="insertEmoji('🤧')">🤧</span>
            <span class="emoji-item" onclick="insertEmoji('😷')">😷</span>
            <span class="emoji-item" onclick="insertEmoji('🤒')">🤒</span>
            <span class="emoji-item" onclick="insertEmoji('🤕')">🤕</span>
            <span class="emoji-item" onclick="insertEmoji('🤑')">🤑</span>
            <span class="emoji-item" onclick="insertEmoji('🤠')">🤠</span>
            <span class="emoji-item" onclick="insertEmoji('😈')">😈</span>
            <span class="emoji-item" onclick="insertEmoji('👿')">👿</span>
            <span class="emoji-item" onclick="insertEmoji('👹')">👹</span>
            <span class="emoji-item" onclick="insertEmoji('👺')">👺</span>
            <span class="emoji-item" onclick="insertEmoji('🤡')">🤡</span>
            <span class="emoji-item" onclick="insertEmoji('💩')">💩</span>
            <span class="emoji-item" onclick="insertEmoji('👻')">👻</span>
            <span class="emoji-item" onclick="insertEmoji('💀')">💀</span>
            <span class="emoji-item" onclick="insertEmoji('☠️')">☠️</span>
            <span class="emoji-item" onclick="insertEmoji('👽')">👽</span>
            <span class="emoji-item" onclick="insertEmoji('👾')">👾</span>
            <span class="emoji-item" onclick="insertEmoji('🤖')">🤖</span>
            <span class="emoji-item" onclick="insertEmoji('🎃')">🎃</span>
            <span class="emoji-item" onclick="insertEmoji('👋')">👋</span>
            <span class="emoji-item" onclick="insertEmoji('👍')">👍</span>
            <span class="emoji-item" onclick="insertEmoji('👎')">👎</span>
            <span class="emoji-item" onclick="insertEmoji('👏')">👏</span>
            <span class="emoji-item" onclick="insertEmoji('🙌')">🙌</span>
            <span class="emoji-item" onclick="insertEmoji('🙏')">🙏</span>
            <span class="emoji-item" onclick="insertEmoji('❤️')">❤️</span>
            <span class="emoji-item" onclick="insertEmoji('💛')">💛</span>
            <span class="emoji-item" onclick="insertEmoji('💚')">💚</span>
            <span class="emoji-item" onclick="insertEmoji('💙')">💙</span>
            <span class="emoji-item" onclick="insertEmoji('💜')">💜</span>
            <span class="emoji-item" onclick="insertEmoji('💯')">💯</span>
            <span class="emoji-item" onclick="insertEmoji('💥')">💥</span>
            <span class="emoji-item" onclick="insertEmoji('💫')">💫</span>
            <span class="emoji-item" onclick="insertEmoji('💬')">💬</span>
            <span class="emoji-item" onclick="insertEmoji('💭')">💭</span>
            <span class="emoji-item" onclick="insertEmoji('💤')">💤</span>
          </div>
        </div>

        <div class="input-group">
          <div class="input-tools">
            <button class="tool-btn" onclick="toggleEmojiPanel()" title="表情">😀</button>
            <button class="tool-btn" onclick="triggerImageUpload()" title="上传图片">📷</button>
            <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
          </div>
          <textarea class="input-text" id="messageInput" placeholder="输入回复内容..." rows="1"></textarea>
          <button class="send-btn" onclick="window.sendMessage()">发送</button>
        </div>
      </div>
    </div>
  </div>

  <script src="/js/jquery/jquery.min.js"></script>
  <script src="/js/layui/layui.js"></script>
  <script>
    layui.use(['layer'], function(){
      var layer = layui.layer;

      // 全局变量
      var currentUserId = null;
      var messageCheckInterval = null;
      var statsCheckInterval = null;

      // 加载活跃用户列表
      function loadActiveUsers() {
        $.get('/hide/customer_chat.php?xtype=get_active_users', function(res) {
          try {
            if (typeof res === 'string') {
              res = JSON.parse(res);
            }

            if(res.code === 1) {
              renderUserList(res.data);
            } else {
              $('#userList').html('<div style="padding: 20px; text-align: center; color: #999;">暂无活跃用户</div>');
            }
          } catch(e) {
            $('#userList').html('<div style="padding: 20px; text-align: center; color: #f56c6c;">加载失败</div>');
          }
        }).fail(function() {
          $('#userList').html('<div style="padding: 20px; text-align: center; color: #f56c6c;">网络错误</div>');
        });
      }

      // 渲染用户列表
      function renderUserList(users) {
        var html = '';

        users.forEach(function(user) {
          var unreadBadge = user.unread_count > 0 ?
            '<span class="unread-badge">' + user.unread_count + '</span>' : '';

          html += '<div class="user-item" onclick="selectUser(' + user.userid + ', \'' + user.username + '\')">' +
            '<div class="user-info">' +
            '<div>' +
            '<div class="user-name">' + user.username + ' (ID: ' + user.userid + ')</div>' +
            '<div class="user-last-message">' + (user.last_message ? (user.last_message.length > 30 ? user.last_message.substring(0, 30) + '...' : user.last_message) : '暂无消息') + '</div>' +
            '</div>' +
            '<div class="user-meta">' +
            '<div class="user-time">' + user.last_message_formatted + '</div>' +
            unreadBadge +
            '</div>' +
            '</div>' +
            '</div>';
        });

        if (html === '') {
          html = '<div style="padding: 20px; text-align: center; color: #999;">暂无活跃用户</div>';
        }

        $('#userList').html(html);
      }

      // 选择用户
      window.selectUser = function(userid, username) {
        currentUserId = userid;

        // 更新选中状态
        $('.user-item').removeClass('active');
        event.currentTarget.classList.add('active');

        // 更新聊天头部
        $('#chatUserInfo').text(username + ' (ID: ' + userid + ')');
        $('#refreshBtn').show();
        $('#chatInput').show();

        // 加载对话记录
        loadConversation(userid);
      };

      // 加载对话记录
      function loadConversation(userid) {
        $('#chatMessages').html('<div class="loading">加载中...</div>');

        $.get('/hide/customer_chat.php?xtype=get_conversation&userid=' + userid, function(res) {
          try {
            if (typeof res === 'string') {
              res = JSON.parse(res);
            }

            if(res.code === 1) {
              renderMessages(res.data.messages);
              if (res.data.user_info) {
                $('#chatUserInfo').html(res.data.user_info.username + ' (ID: ' + userid + ') - 余额: ¥' + parseFloat(res.data.user_info.balance).toFixed(2));
              }
            } else {
              $('#chatMessages').html('<div style="padding: 20px; text-align: center; color: #f56c6c;">加载失败</div>');
            }
          } catch(e) {
            $('#chatMessages').html('<div style="padding: 20px; text-align: center; color: #f56c6c;">数据处理异常</div>');
          }
        }).fail(function() {
          $('#chatMessages').html('<div style="padding: 20px; text-align: center; color: #f56c6c;">网络错误</div>');
        });
      }

      // 渲染消息
      function renderMessages(messages) {
        var html = '';

        messages.forEach(function(msg) {
          var isAdmin = msg.sender_type === 'admin';
          var avatar = isAdmin ? '客' : '用';
          var messageClass = isAdmin ? 'message admin' : 'message';

          var messageContent = '';
          if (msg.message_type === 'image' && msg.image_url) {
            // 图片消息
            messageContent = '<div class="message-content">' +
              (msg.message ? '<div style="margin-bottom: 8px;">' + msg.message + '</div>' : '') +
              '<img src="' + msg.image_url + '" class="message-image" onclick="previewImage(\'' + msg.image_url + '\')" alt="图片">' +
              '</div>';
          } else {
            // 文字消息
            messageContent = '<div class="message-content">' + msg.message + '</div>';
          }

          html += '<div class="' + messageClass + '">' +
            '<div class="message-avatar">' + avatar + '</div>' +
            '<div>' +
            messageContent +
            '<div class="message-time">' + msg.tjtime + '</div>' +
            '</div>' +
            '</div>';
        });

        if (html === '') {
          html = '<div style="padding: 20px; text-align: center; color: #999;">暂无消息</div>';
        }

        $('#chatMessages').html(html);

        // 滚动到底部
        var chatMessages = document.getElementById('chatMessages');
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }

      // 发送消息
      window.sendMessage = function() {
        if (!currentUserId) {
          layer.msg('请先选择用户', {icon: 2});
          return;
        }

        var message = $('#messageInput').val().trim();
        if (!message) {
          layer.msg('请输入回复内容', {icon: 2});
          return;
        }

        var loading = layer.load(2);

        $.post('/hide/customer_chat.php?xtype=send_message', {
          userid: currentUserId,
          message: message
        }, function(res) {
          layer.close(loading);

          try {
            if (typeof res === 'string') {
              res = JSON.parse(res);
            }
            if(res.code === 1) {
              $('#messageInput').val('');
              loadConversation(currentUserId);
              loadActiveUsers(); // 刷新用户列表
            } else {
              layer.msg(res.msg || '发送失败', {icon: 2});
            }
          } catch(e) {
            layer.msg('数据处理异常', {icon: 2});
          }
        }).fail(function() {
          layer.close(loading);
          layer.msg('网络请求失败', {icon: 2});
        });
      };

      // 快速回复
      window.sendQuickReply = function(templateId) {
        if (!currentUserId) {
          layer.msg('请先选择用户', {icon: 2});
          return;
        }

        var loading = layer.load(2);

        $.post('/hide/customer_chat.php?xtype=quick_reply', {
          userid: currentUserId,
          template_id: templateId
        }, function(res) {
          layer.close(loading);

          try {
            if (typeof res === 'string') {
              res = JSON.parse(res);
            }
            if(res.code === 1) {
              loadConversation(currentUserId);
              loadActiveUsers(); // 刷新用户列表
            } else {
              layer.msg(res.msg || '发送失败', {icon: 2});
            }
          } catch(e) {
            layer.msg('数据处理异常', {icon: 2});
          }
        }).fail(function() {
          layer.close(loading);
          layer.msg('网络请求失败', {icon: 2});
        });
      };

      // 刷新当前对话
      window.refreshChat = function() {
        if (currentUserId) {
          loadConversation(currentUserId);
        }
        loadActiveUsers();
      };

      // 更新统计数据
      function updateStats() {
        $.get('/hide/customer_chat.php?xtype=get_stats', function(res) {
          try {
            if (typeof res === 'string') {
              res = JSON.parse(res);
            }
            if(res.code === 1) {
              $('#onlineUsers').text(res.data.online_users);
              $('#unreadCount').text(res.data.unread_messages);
              $('#todayMessages').text(res.data.today_messages);
            }
          } catch(e) {
            // 静默处理错误
          }
        });
      }

      // 消息输入框回车发送
      $('#messageInput').keydown(function(e) {
        if (e.keyCode === 13 && !e.shiftKey) {
          e.preventDefault();
          window.sendMessage();
        }
      });

      // 自动调整输入框高度
      $('#messageInput').on('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 100) + 'px';
      });

      // 用户搜索
      $('#userSearch').on('input', function() {
        var keyword = $(this).val().toLowerCase();
        $('.user-item').each(function() {
          var text = $(this).text().toLowerCase();
          if (text.indexOf(keyword) !== -1) {
            $(this).show();
          } else {
            $(this).hide();
          }
        });
      });

      // 播放通知音
      function playNotificationSound() {
        try {
          let audio = new Audio('/js/dd.mp3');
          audio.volume = 0.7; // 设置音量为70%
          audio.play().catch(function() {
            // 如果播放失败，尝试备用提示音
            try {
              let fallbackAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
              fallbackAudio.play();
            } catch (e) {
              // 忽略错误
            }
          });
        } catch (e) {
          // 忽略错误
        }
      }

      // 检查是否有新用户消息
      var lastUnreadCount = 0;
      function checkForNewUserMessages() {
        $.get('/hide/customer_chat.php?xtype=get_stats', function(res) {
          try {
            if (typeof res === 'string') {
              res = JSON.parse(res);
            }
            if(res.code === 1) {
              var currentUnreadCount = res.data.unread_messages;
              // 如果未读消息数量增加，播放提示音
              if (currentUnreadCount > lastUnreadCount && lastUnreadCount > 0) {
                playNotificationSound();
              }
              lastUnreadCount = currentUnreadCount;
            }
          } catch(e) {
            // 静默处理错误
          }
        });
      }

      // 定时检查新消息和更新统计
      function startAutoRefresh() {
        // 每5秒刷新用户列表
        messageCheckInterval = setInterval(function() {
          loadActiveUsers();
          if (currentUserId) {
            loadConversation(currentUserId);
          }
          // 检查新用户消息
          checkForNewUserMessages();
        }, 5000);

        // 每10秒更新统计
        statsCheckInterval = setInterval(updateStats, 10000);
      }

      // 停止自动刷新
      function stopAutoRefresh() {
        if (messageCheckInterval) {
          clearInterval(messageCheckInterval);
          messageCheckInterval = null;
        }
        if (statsCheckInterval) {
          clearInterval(statsCheckInterval);
          statsCheckInterval = null;
        }
      }

      // 页面可见性变化处理
      document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
          stopAutoRefresh();
        } else {
          startAutoRefresh();
          loadActiveUsers();
          updateStats();
        }
      });

      // 页面卸载时清理
      window.addEventListener('beforeunload', function() {
        stopAutoRefresh();
      });

      // 表情包面板切换
      window.toggleEmojiPanel = function() {
        const panel = document.getElementById('emojiPanel');
        if (panel.style.display === 'none') {
          panel.style.display = 'block';
        } else {
          panel.style.display = 'none';
        }
      };

      // 插入表情
      window.insertEmoji = function(emoji) {
        const input = document.getElementById('messageInput');
        const cursorPos = input.selectionStart;
        const textBefore = input.value.substring(0, cursorPos);
        const textAfter = input.value.substring(cursorPos);

        input.value = textBefore + emoji + textAfter;
        input.focus();
        input.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

        // 关闭表情面板
        document.getElementById('emojiPanel').style.display = 'none';
      };

      // 触发图片上传
      window.triggerImageUpload = function() {
        document.getElementById('imageInput').click();
      };

      // 处理图片上传
      window.handleImageUpload = function(event) {
        const file = event.target.files[0];
        if (!file) return;

        // 检查文件类型
        if (!file.type.startsWith('image/')) {
          layer.msg('请选择图片文件！', {icon: 2});
          return;
        }

        // 检查文件大小（限制5MB）
        if (file.size > 5 * 1024 * 1024) {
          layer.msg('图片大小不能超过5MB！', {icon: 2});
          return;
        }

        // 显示上传预览
        const reader = new FileReader();
        reader.onload = function(e) {
          showImagePreview(e.target.result, file);
        };
        reader.readAsDataURL(file);
      };

      // 显示图片预览
      function showImagePreview(dataUrl, file) {
        const previewHtml = `
          <div class="image-upload-preview" id="imagePreview">
            <img src="${dataUrl}" class="image-preview" alt="预览图片">
            <button class="image-remove" onclick="removeImagePreview()">×</button>
            <div class="loading-overlay" style="display: none;">
              <span>上传中...</span>
            </div>
          </div>
        `;

        // 在输入框上方显示预览
        const inputGroup = document.querySelector('.input-group');
        inputGroup.insertAdjacentHTML('beforebegin', previewHtml);

        // 存储文件数据
        window.pendingImageFile = file;
        window.pendingImageData = dataUrl;
      }

      // 移除图片预览
      window.removeImagePreview = function() {
        const preview = document.getElementById('imagePreview');
        if (preview) {
          preview.remove();
        }
        window.pendingImageFile = null;
        window.pendingImageData = null;
        document.getElementById('imageInput').value = '';
      };

      // 修改发送消息函数以支持图片
      const originalSendMessage = window.sendMessage;
      window.sendMessage = function() {
        if (!currentUserId) {
          layer.msg('请先选择用户', {icon: 2});
          return;
        }

        const messageText = $('#messageInput').val().trim();
        const hasImage = window.pendingImageFile;

        if (!messageText && !hasImage) {
          layer.msg('请输入消息内容或选择图片', {icon: 2});
          return;
        }

        // 如果有图片，先上传图片
        if (hasImage) {
          uploadImageAndSend(messageText);
        } else {
          // 只发送文字消息，调用原来的函数
          originalSendMessage();
        }
      };

      // 上传图片并发送消息
      function uploadImageAndSend(messageText) {
        const formData = new FormData();
        formData.append('image', window.pendingImageFile);
        formData.append('message', messageText);
        formData.append('type', 'image');

        // 显示上传状态
        const loadingOverlay = document.querySelector('.loading-overlay');
        if (loadingOverlay) {
          loadingOverlay.style.display = 'flex';
        }

        var loading = layer.load(2);

        $.ajax({
          url: '/hide/customer_chat.php?xtype=upload_image',
          type: 'POST',
          data: formData,
          processData: false,
          contentType: false,
          success: function(res) {
            layer.close(loading);

            if (typeof res === 'string') {
              res = JSON.parse(res);
            }

            if (res.code === 1) {
              // 发送包含图片的消息
              sendImageMessage(messageText, res.data.image_url);
            } else {
              layer.msg(res.msg || '图片上传失败', {icon: 2});
            }
          },
          error: function() {
            layer.close(loading);
            layer.msg('图片上传失败，请重试', {icon: 2});
          }
        });
      }

      // 发送图片消息
      function sendImageMessage(messageText, imageUrl) {
        $.post('/hide/customer_chat.php?xtype=send_message', {
          userid: currentUserId,
          message: messageText,
          message_type: 'image',
          image_url: imageUrl
        }, function(res) {
          if (typeof res === 'string') {
            res = JSON.parse(res);
          }

          if (res.code === 1) {
            $('#messageInput').val('');
            removeImagePreview();
            loadConversation(currentUserId);
            loadActiveUsers(); // 刷新用户列表
          } else {
            layer.msg(res.msg || '发送失败', {icon: 2});
          }
        }).fail(function() {
          layer.msg('网络请求失败', {icon: 2});
        });
      }

      // 图片预览功能
      window.previewImage = function(imageUrl) {
        const modal = document.createElement('div');
        modal.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          cursor: pointer;
        `;

        const img = document.createElement('img');
        img.src = imageUrl;
        img.style.cssText = `
          max-width: 90%;
          max-height: 90%;
          border-radius: 8px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;

        modal.appendChild(img);
        document.body.appendChild(modal);

        modal.onclick = function() {
          document.body.removeChild(modal);
        };
      };

      // 点击空白处关闭表情面板
      document.addEventListener('click', function(e) {
        const emojiPanel = document.getElementById('emojiPanel');
        const emojiBtn = e.target.closest('.tool-btn');

        if (emojiPanel && emojiPanel.style.display === 'block' && !emojiPanel.contains(e.target) && !emojiBtn) {
          emojiPanel.style.display = 'none';
        }
      });

      // 移动端面板切换功能
      window.toggleMobilePanel = function() {
        const userPanel = document.getElementById('userPanel');
        const overlay = document.querySelector('.mobile-overlay');

        if (userPanel.classList.contains('mobile-open')) {
          closeMobilePanel();
        } else {
          userPanel.classList.add('mobile-open');
          overlay.classList.add('active');
          document.body.style.overflow = 'hidden';
        }
      };

      window.closeMobilePanel = function() {
        const userPanel = document.getElementById('userPanel');
        const overlay = document.querySelector('.mobile-overlay');

        userPanel.classList.remove('mobile-open');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
      };

      // 选择用户后自动关闭移动端面板
      const originalSelectUser = window.selectUser;
      window.selectUser = function(userid, username) {
        originalSelectUser(userid, username);

        // 在移动端选择用户后关闭面板
        if (window.innerWidth <= 768) {
          closeMobilePanel();
        }
      };

      // 窗口大小改变时的处理
      window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
          // 桌面端时确保面板正常显示
          const userPanel = document.getElementById('userPanel');
          const overlay = document.querySelector('.mobile-overlay');

          userPanel.classList.remove('mobile-open');
          overlay.classList.remove('active');
          document.body.style.overflow = '';
        }
      });

      // 阻止移动端面板内的点击事件冒泡
      document.getElementById('userPanel').addEventListener('click', function(e) {
        e.stopPropagation();
      });

      // 初始化
      loadActiveUsers();
      // 初始化未读消息数量
      updateStats();
      setTimeout(function() {
        // 延迟1秒后开始监听新消息，避免初始化时播放提示音
        $.get('/hide/customer_chat.php?xtype=get_stats', function(res) {
          try {
            if (typeof res === 'string') {
              res = JSON.parse(res);
            }
            if(res.code === 1) {
              lastUnreadCount = res.data.unread_messages;
            }
          } catch(e) {
            // 静默处理错误
          }
        });
        startAutoRefresh();
      }, 1000);
    });
  </script>
</body>
</html>
