<?php
include('../data/comm.inc.php');
include('../data/myadminvar.php');
include('../func/func.php');
include('../func/csfunc.php');
include('../func/adminfunc.php');
include('../include.php');
include('./checklogin.php');
switch ($_REQUEST['xtype']) {
    case "bank":
        $bank = $msql->arr("select * from `$tb_bank` order by id ", 1);
        $tpl->assign("bank", $bank);
        $tpl->display("money_bank.html");
        break;
    case "setbank":
        $str = str_replace('\\', '', $_POST['str']);
        $arr = json_decode($str, true);
        foreach ($arr as $v) {
            if ($v["bankid"] == '') {
                $bankid   = setupid3($tb_bank, 'bankid');
                $bankname = trim($v["bankname"]);
                $en       = trim($v["en"]);
                $msql->query("insert into `$tb_bank` set bankid='$bankid',bankname='$bankname',en='$en'");
            } else {
                $bankid   = $v["bankid"];
                $bankname = trim($v["bankname"]);
                $en       = trim($v["en"]);
                $msql->query("update `$tb_bank` set bankname='$bankname',en='$en' where bankid='$bankid'");
            }
        }
        echo 1;
        break;
    case "dbank":
        $bankid = $_POST["bankid"];
        $msql->query("select 1 from `$tb_banknum` where bankid='$bankid'");
        $msql->next_record();
        if ($msql->f(0) == 1) {
            echo 2;
        } else {
            $msql->query("delete from `$tb_bank` where bankid='$bankid'");
            echo 1;
        }
        break;
    case "chongzhifs":
        $msql->query("select bankonline,bankatm,weixin,alipay from `$tb_config` ");
        $msql->next_record();
        $tpl->assign("bankonline", $msql->f("bankonline"));
        $tpl->assign("bankatm", $msql->f("bankatm"));
        $tpl->assign("weixin", $msql->f("weixin"));
        $tpl->assign("alipay", $msql->f("alipay"));
        $tpl->display("money_chongzhifs.html");
        break;
    case "setchongzhifs":
        $bankonline = isnum($_POST['bankonline']);
        $bankatm    = isnum($_POST['bankatm']);
        $weixin     = isnum($_POST['weixin']);
        $alipay     = isnum($_POST['alipay']);
        $msql->query("update `$tb_config` set bankonline='$bankonline',bankatm='$bankatm',weixin='$weixin',alipay='$alipay'");
        echo 1;
        break;
    case "banknum":
        $uid = $_REQUEST['uid'];
        if ($uid == '')
            $uid = $userid;
        $bank = $msql->arr("select * from `$tb_banknum` where userid='$uid'", 1);
        foreach ($bank as $k => $v) {
            $bank[$k]["username"] = transu($v["userid"]);
            $bank[$k]["bankname"] = transbank($v["bankid"]);
        }
        $tpl->assign("bank", $bank);
        $tpl->assign("banks", getbank());
        $tpl->assign("moneyuser", getmoneyuser());
        $tpl->assign("uid", $uid);
        $tpl->display("money_banknum.html");
        break;
    case "setbanknum":
        $str = str_replace('\\', '', $_POST['str']);
        $arr = json_decode($str, true);
        foreach ($arr as $v) {
            if ($v["id"] == 'new') {
                $bankid    = trim($v["bankid"]);
                $uid       = trim($v["uid"]);
                $num       = trim($v["num"]);
                $name      = trim($v["name"]);
                $kaihuhang = trim($v["num"]);
                $bankpass  = trim($v["bankpass"]);
                $ifok      = trim($v["ifok"]);
                $msql->query("insert into `$tb_banknum` set bankid='$bankid',name='$name',num='$num',kaihuhang='$kaihuhang',bankpass='$bankpass',ifok='$ifok',userid='$uid'");
            } else {
                $id        = trim($v["id"]);
                $uid       = trim($v["uid"]);
                $num       = trim($v["num"]);
                $name      = trim($v["name"]);
                $kaihuhang = trim($v["kaihuhang"]);
                $bankpass  = trim($v["bankpass"]);
                $ifok      = trim($v["ifok"]);
                $msql->query("update `$tb_banknum` set name='$name',num='$num',kaihuhang='$kaihuhang',bankpass='$bankpass',ifok='$ifok' where id='$id' and userid='$uid'");
            }
        }
        echo 1;
        break;
    case "dbanknum":
        $id  = $_POST["id"];
        $uid = $_POST["uid"];
        $msql->query("delete from `$tb_banknum` where id='$id' and userid='$uid'");
        echo 1;
        break;
    case "moneyuser":
        $upage    = $_REQUEST['upage'];
        $status   = $_REQUEST['status'];
        $online   = $_REQUEST['online'];
        $username = trim($_REQUEST['username']);
        $fid      = $_REQUEST['fid'];
        if (checkid($fid)) {
            $whi = " and fid='$fid' ";
        }
        if ($username != '') {
            $whi .= "  and  (username like '%$username%' or name like '%$username%' or tname like '%$username%' or userid='$username')  ";
        }
        if ($status != 'all' & $status != '') {
            $whi .= " and status='$status' ";
        }
        if ($online == 1) {
            $whi .= " and online=1 ";
        }
        $msql->query("select count(id) from `$tb_user` where fudong=1 and wid in(select wid from `$tb_web` where moneytype=1) " . $whi);
        $msql->next_record();
        $rcount = $msql->f(0);
        $psize  = $config['psize1'];
        $upage  = r1p($upage);
        $pcount = $rcount % $psize == 0 ? $rcount / $psize : (($rcount - $rcount % $psize) / $psize + 1);
        if ($upage > $pcount)
            $upage = 1;
        if ($upage < 1)
            $upage = 1;
        $tpl->assign("rcount", $rcount);
        $tpl->assign("pcount", $pcount);
        $tpl->assign("upage", $upage);
        $msql->query("select * from `$tb_user` where fudong=1 and wid in(select wid from `$tb_web` where moneytype=1) " . $whi . " order by lastlogintime desc limit " . ($upage - 1) * $psize . "," . $psize);
        $user = array();
        $i    = 0;
        while ($msql->next_record()) {
            $user[$i]['userid']        = $msql->f('userid');
            $user[$i]['username']      = $msql->f('username');
            $user[$i]['online']        = $msql->f('online');
            $user[$i]['tname']         = $msql->f('tname');
            $user[$i]['qq']            = $msql->f('qq');
            $user[$i]['tel']           = $msql->f('tel');
            $user[$i]['sex']           = $msql->f('sex');
            $user[$i]['birthday']      = $msql->f('birthday');
            $user[$i]['regtime']       = substr($msql->f('regtime'), 0, 10);
            $user[$i]['lastlogintime'] = substr($msql->f('lastlogintime'), 0, 10);
            $user[$i]['status']        = $msql->f('status');
            $user[$i]['statusz']       = transstatus($msql->f('status'));
            $user[$i]['kmaxmoney']     = $msql->f('kmaxmoney');
            $user[$i]['kmoney']        = $msql->f('kmoney');
            $user[$i]['wid']           = $msql->f('wid');
            $fsql->query("select layer,webname,maxlayer from `$tb_web` where wid='" . $msql->f('wid') . "'");
            $fsql->next_record();
            $user[$i]['web'] = $fsql->f('webname');
            if ($msql->f('layer') == 1) {
                $user[$i]['upuser'] = "admin";
            } else {
                $fsql->query("select username,tname from `$tb_user` where userid='" . $msql->f('fid') . "'");
                $fsql->next_record();
                $user[$i]['upuser'] = $fsql->f('username');
                $user[$i]['upname'] = $fsql->f('tname');
            }
            $user[$i]['fid']  = $msql->f('fid');
            $user[$i]['fids'] = array();
            for ($j = 0; $j < $msql->f('layer') - 1; $j++) {
                $user[$i]['fids'][] = transu($msql->f('fid' . ($j + 1)));
            }
            $fsql->query("select count(id) from `$tb_user` where fid='" . $msql->f('userid') . "' ");
            $fsql->next_record();
            $user[$i]['downnum'] = r0($fsql->f(0));
            $i++;
        }
        $tpl->assign("user", $user);
        $tpl->assign("online", $online);
        $tpl->display("money_moneyuser.html");
        break;
    case "userxx":
        $uid = $_POST['uid'];
        if (!checkid($uid))
            exit;
        $msql->query("select * from `$tb_user` where userid='$uid'");
        $msql->next_record();
        $user             = array();
        $user["shengshi"] = $msql->f("shengshi");
        $user["street"]   = $msql->f("street");
        $user["shr"]      = $msql->f("shr");
        $user["bz"]       = $msql->f("bz");
        $fsql->query("select * from `$tb_banknum` where userid='$uid'");
        $i            = 0;
        $user["bank"] = array();
        while ($fsql->next_record()) {
            $user["bank"][$i]["bank"]      = transbank($fsql->f('bankid'));
            $user["bank"][$i]["name"]      = $fsql->f("name");
            $user["bank"][$i]["kaihuhang"] = $fsql->f("kaihuhang");
            $user["bank"][$i]["num"]       = $fsql->f("num");
            $user["bank"][$i]["ifok"]      = $fsql->f("ifok");
            $i++;
        }
        echo json_encode($user);
        break;
    case "chongzhi":
        $sdate = rdates($_REQUEST['sdate']);
        $edate = rdates($_REQUEST['edate']);
        $tpl->assign("sdate", $sdate);
        $tpl->assign("edate", $edate);
        $upage    = $_REQUEST['upage'];
        $status   = $_REQUEST['status'];
        $username = strip_tags(($_REQUEST['username']));
        if ($status != '' & ($status == 0 | $status == 1 | $status == 2)) {
            $whi .= " and status='$status' ";
        }
        if ($username != '') {
            $whi .= " and userid in (select userid from `$tb_user` where username like '%$username%' or name like '%$username%' or tname like '%$username%' or userid='$username') ";
        }
        if ($sdate != '' & $edate != '') {
            $sdate = $sdate . " 00:00:00";
            $edate = $edate . " 23:59:59";
            $whi .= " and tjtime>='$sdate' and tjtime<='$edate' ";
        }
        $msql->query("select count(id) from `$tb_money` where mtype=0  " . $whi);
        $msql->next_record();
        $rcount = $msql->f(0);
        $psize  = $config['psize1'];
        $upage  = r1p($upage);
        $pcount = $rcount % $psize == 0 ? $rcount / $psize : (($rcount - $rcount % $psize) / $psize + 1);
        if ($upage > $pcount)
            $upage = 1;
        if ($upage < 1)
            $upage = 1;
        $tpl->assign("rcount", $rcount);
        $tpl->assign("pcount", $pcount);
        $tpl->assign("upage", $upage);
        $msql->query("select * from `$tb_money` where mtype=0 " . $whi . " order by tjtime desc limit " . ($upage - 1) * $psize . "," . $psize);
        $marr = array();
        $i    = 0;
        while ($msql->next_record()) {
            $marr[$i]['ustatus'] = $msql->f("status");
            $marr[$i]['status']  = moneystatus($msql->f("status"));
            $marr[$i]['mtype']   = moneymtype($msql->f("mtype"));
            $marr[$i]['money']   = $msql->f("money");
            $marr[$i]['sxfei']   = $msql->f("sxfei");
            $marr[$i]['tjtime']  = $msql->f("tjtime");
            $marr[$i]['bz']      = $msql->f("bz");
            $marr[$i]['ms']      = $msql->f("ms");
            // 处理支付方式显示 - 优先使用新的payment_method字段
            $payment_method = $msql->f("payment_method");
            if ($payment_method) {
                // 使用新的支付方式字段
                switch ($payment_method) {
                    case 'bank':
                        $marr[$i]['fs'] = '银行转账';
                        break;
                    case 'alipay':
                        $marr[$i]['fs'] = '支付宝';
                        break;
                    case 'wechat':
                        $marr[$i]['fs'] = '微信支付';
                        break;
                    case 'usdt':
                        $marr[$i]['fs'] = 'USDT';
                        break;
                    default:
                        $marr[$i]['fs'] = $payment_method;
                        break;
                }
            } else {
                // 兼容旧的fs字段
                $marr[$i]['fs'] = moneyfs($msql->f("fs"));
            }

            $marr[$i]['bank']    = $msql->f("bank");
            $marr[$i]['sname']   = $msql->f('sname');
            $marr[$i]['snum']    = $msql->f('snum');
            $marr[$i]['uname']   = $msql->f('uname');
            $marr[$i]['unum']    = $msql->f('unum');
            $marr[$i]['cuntime'] = $msql->f("cuntime");
            $marr[$i]['pass']    = $msql->f("pass");
            $marr[$i]['id']      = $msql->f("id");
            $marr[$i]['userid']  = $msql->f("userid");
            // 新增字段
            $marr[$i]['payment_method'] = $payment_method ?: 'bank';
            $marr[$i]['actual_amount'] = $msql->f("actual_amount") ?: $msql->f("money");
            $marr[$i]['alipay_account'] = $msql->f("alipay_account");
            $marr[$i]['alipay_name'] = $msql->f("alipay_name");
            $marr[$i]['alipay_qr_code'] = $msql->f("alipay_qr_code");
            $marr[$i]['wechat_account'] = $msql->f("wechat_account");
            $marr[$i]['wechat_name'] = $msql->f("wechat_name");
            $marr[$i]['wechat_qr_code'] = $msql->f("wechat_qr_code");
            $marr[$i]['usdt_amount'] = $msql->f("usdt_amount");
            $marr[$i]['usdt_address'] = $msql->f("usdt_address");
            $marr[$i]['usdt_network'] = $msql->f("usdt_network");
            $marr[$i]['exchange_rate'] = $msql->f("exchange_rate");
            $marr[$i]['proof_image'] = $msql->f("proof_image");
            $marr[$i]['receiver_qr_code'] = $msql->f("receiver_qr_code");
            $marr[$i]['bank_branch'] = $msql->f("bank_branch");
            $fsql->query("select username,tname from `$tb_user` where userid='" . $msql->f("userid") . "'");
            $fsql->next_record();
            $marr[$i]['username'] = $fsql->f("username");
            $marr[$i]['tname']    = $fsql->f("tname");
            $marr[$i]['tjid']     = $msql->f("tjid");
            $fsql->query("select username,tname from `$tb_user` where userid='" . $msql->f("tjid") . "'");
            $fsql->next_record();
            $marr[$i]['tjname'] = $fsql->f("username");
            if (substr($msql->f("cltime"), 0, 1) == 0) {
                $marr[$i]['cltime'] = '';
            } else {
                $marr[$i]['cltime'] = $msql->f("cltime");
            }
            $marr[$i]['clid'] = $msql->f("clid");
            $fsql->query("select adminname from `$tb_admins` where adminid='" . $msql->f("clid") . "'");
            $fsql->next_record();
            $marr[$i]['clname'] = $fsql->f("adminname");
            $i++;
        }
        $tpl->assign("marr", $marr);
        for ($i = 1; $i <= $pcount; $i++) {
            $parr[] = $i;
        }
        $tpl->assign("parr", $parr);
        $tpl->assign("status", $status);
        $tpl->assign("hide", $_SESSION['hide']);
        $tpl->display("money_chongzhi.html");
        break;
    case "upcz":
        $id = $_POST['ids'];
        if (!is_numeric($id))
            exit;
        $sxfei = $_POST['sxfei'];
        $ms    = $_POST['ms'];
        $bz    = $_POST['bz'];
        $sql   = "update `$tb_money` set sxfei='$sxfei',ms='$ms',bz='$bz' where id='$id'";
        if ($msql->query($sql)) {
            echo 1;
        }
        break;
    case "delcz":
        if ($_SESSION['hide'] != 1)
            exit;
        $id = explode('|', $_POST["idstr"]);
        foreach ($id as $v) {
            if (is_numeric($v) & $v != '') {
                $msql->query("delete from `$tb_money` where id='$v' and mtype=0");
            }
        }
        echo 1;
        break;
    case "upczstatus":
        $id = $_POST['ids'];
        if (!is_numeric($id))
            exit;
        $status = $_POST["status"];
        if ($status != 1 & $status != 2)
            exit;
        $msql->query("select * from `$tb_money` where id='$id'");
        $msql->next_record();
            if($status ==1){
            $money = (float) ($msql->f('money') - $msql->f('sxfei'));
            $fsql->query("select * from `$tb_user` where userid='" . $msql->f('userid') . "'");
            $fsql->next_record();
            $je = $fsql->f('kmoney') + $money;
             userchange("存入现金额度" . abs($money) . "!原额度" . $msql->f('kmoney') . ",现额度" . $je . "", $msql->f('userid'));
             $fsql->query("update `$tb_user` set kmoney=kmoney+$money where userid=" . $msql->f('userid'));
             
             $fsql->query("update `$tb_money` set cltime=NOW(),clid='$adminid',status=1 where id='$id'");
             echo 1;
            }
            
            if($status ==2){

             $fsql->query("update `$tb_money` set cltime=NOW(),clid='$adminid',status=2 where id='$id'");
             echo 2;
            }
    
        break;
    
    case "tikuan":
        $sdate = rdates($_REQUEST['sdate']);
        $edate = rdates($_REQUEST['edate']);
        $tpl->assign("sdate", $sdate);
        $tpl->assign("edate", $edate);
        $upage    = $_REQUEST['upage'];
        $status   = $_REQUEST['status'];
        $username = strip_tags(($_REQUEST['username']));
        if ($status != '' & ($status == 0 | $status == 1 | $status == 2)) {
            $whi .= " and status='$status' ";
        }
        if ($username != '') {
            $whi .= " and userid in (select userid from `$tb_user` where username like '%$username%' or name like '%$username%' or tname like '%$username%' or userid='$username') ";
        }
        if ($sdate != '' & $edate != '') {
            $sdate = $sdate . " 00:00:00";
            $edate = $edate . " 23:59:59";
            $whi .= " and tjtime>='$sdate' and tjtime<='$edate' ";
        }
        $msql->query("select count(id) from `$tb_money` where mtype=2 " . $whi);
        $msql->next_record();
        $rcount = $msql->f(0);
        $psize  = $config['psize1'];
        $upage  = r1p($upage);
        $pcount = $rcount % $psize == 0 ? $rcount / $psize : (($rcount - $rcount % $psize) / $psize + 1);
        if ($upage > $pcount)
            $upage = 1;
        if ($upage < 1)
            $upage = 1;
        $tpl->assign("rcount", $rcount);
        $tpl->assign("pcount", $pcount);
        $tpl->assign("upage", $upage);
        $msql->query("select * from `$tb_money` where mtype=2 " . $whi . " order by tjtime desc limit " . ($upage - 1) * $psize . "," . $psize);
        $marr = array();
        $i    = 0;
        while ($msql->next_record()) {
            $marr[$i]['ustatus'] = $msql->f("status");
            $marr[$i]['status']  = moneystatus($msql->f("status"));
            $marr[$i]['mtype']   = moneymtype($msql->f("mtype"));
            $marr[$i]['money']   = $msql->f("money");
            $marr[$i]['sxfei']   = $msql->f("sxfei");
            $marr[$i]['tjtime']  = $msql->f("tjtime");
            $marr[$i]['bz']      = $msql->f("bz");
            $marr[$i]['ms']      = $msql->f("ms");
            // 处理支付方式显示 - 优先使用新的payment_method字段
            $payment_method = $msql->f("payment_method");
            if ($payment_method) {
                // 使用新的支付方式字段
                switch ($payment_method) {
                    case 'bank':
                        $marr[$i]['fs'] = '银行转账';
                        break;
                    case 'alipay':
                        $marr[$i]['fs'] = '支付宝';
                        break;
                    case 'wechat':
                        $marr[$i]['fs'] = '微信支付';
                        break;
                    case 'usdt':
                        $marr[$i]['fs'] = 'USDT';
                        break;
                    default:
                        $marr[$i]['fs'] = $payment_method;
                        break;
                }
            } else {
                // 兼容旧的fs字段
                $marr[$i]['fs'] = moneyfs($msql->f("fs"));
            }

            $marr[$i]['bank']    = $msql->f("bank");
            $marr[$i]['sname']   = $msql->f('sname');
            $marr[$i]['snum']    = $msql->f('snum');
            $marr[$i]['uname']   = $msql->f('uname');
            $marr[$i]['unum']    = $msql->f('unum');
            $marr[$i]['cuntime'] = $msql->f("cuntime");
            $marr[$i]['pass']    = $msql->f("pass");
            $marr[$i]['id']      = $msql->f("id");
            $marr[$i]['userid']  = $msql->f("userid");
            // 新增字段
            $marr[$i]['payment_method'] = $payment_method ?: 'bank';
            $marr[$i]['actual_amount'] = $msql->f("actual_amount") ?: $msql->f("money");
            $marr[$i]['alipay_account'] = $msql->f("alipay_account");
            $marr[$i]['alipay_name'] = $msql->f("alipay_name");
            $marr[$i]['alipay_qr_code'] = $msql->f("alipay_qr_code");
            $marr[$i]['wechat_account'] = $msql->f("wechat_account");
            $marr[$i]['wechat_name'] = $msql->f("wechat_name");
            $marr[$i]['wechat_qr_code'] = $msql->f("wechat_qr_code");
            $marr[$i]['usdt_amount'] = $msql->f("usdt_amount");
            $marr[$i]['usdt_address'] = $msql->f("usdt_address");
            $marr[$i]['usdt_network'] = $msql->f("usdt_network");
            $marr[$i]['exchange_rate'] = $msql->f("exchange_rate");
            $marr[$i]['proof_image'] = $msql->f("proof_image");
            $marr[$i]['receiver_qr_code'] = $msql->f("receiver_qr_code");
            $marr[$i]['bank_branch'] = $msql->f("bank_branch");
            $fsql->query("select username,tname from `$tb_user` where userid='" . $msql->f("userid") . "'");
            $fsql->next_record();
            $marr[$i]['username'] = $fsql->f("username");
            $marr[$i]['tname']    = $fsql->f("tname");
            $marr[$i]['tjid']     = $msql->f("tjid");
            $fsql->query("select username,tname from `$tb_user` where userid='" . $msql->f("tjid") . "'");
            $fsql->next_record();
            $marr[$i]['tjname'] = $fsql->f("username");
            if (substr($msql->f("cltime"), 0, 1) == 0) {
                $marr[$i]['cltime'] = '';
            } else {
                $marr[$i]['cltime'] = $msql->f("cltime");
            }
            $marr[$i]['clid'] = $msql->f("clid");
            $fsql->query("select adminname from `$tb_admins` where adminid='" . $msql->f("clid") . "'");
            $fsql->next_record();
            $marr[$i]['clname'] = $fsql->f("adminname");
            $i++;
        }
        $tpl->assign("marr", $marr);
        for ($i = 1; $i <= $pcount; $i++) {
            $parr[] = $i;
        }
        $tpl->assign("parr", $parr);
        $tpl->assign("status", $status);
        $tpl->assign("hide", $_SESSION['hide']);
        $tpl->display("money_tikuan.html");
        break;
    case "tikuansave":
        $id = $_POST['ids'];
        if (!is_numeric($id))
            exit;
        $status = $_POST["status"];
        if ($status != 1 & $status != 2)
            exit;

        // 获取提现订单信息
        $msql->query("SELECT userid, money, sxfei, status, mtype FROM `$tb_money` WHERE id='$id'");
        if (!$msql->next_record()) {
            echo 0;
            exit;
        }

        $userid = $msql->f('userid');
        $money = $msql->f('money');
        $sxfei = $msql->f('sxfei');
        $old_status = $msql->f('status');
        $mtype = $msql->f('mtype');

        // 只处理提现订单(mtype=2)且状态为待处理(status=0)的订单
        if ($mtype != 2 || $old_status != 0) {
            echo 0;
            exit;
        }

        // 开始事务
        $msql->query("START TRANSACTION");

        try {
            if ($status == 1) {
                // 审核通过：只更新状态，不扣款（用户提交时已经扣过了）
                $msql->query("UPDATE `$tb_money` SET status=1, cltime=NOW(), clid='$adminid' WHERE id='$id'");

                // 记录审核通过日志
                userchange("提现申请审核通过，金额：¥$money", $userid);
                echo 1;

            } elseif ($status == 2) {
                // 审核拒绝：退还资金给用户
                $msql->query("UPDATE `$tb_money` SET status=2, cltime=NOW(), clid='$adminid' WHERE id='$id'");

                // 退还提现金额给用户
                $fsql->query("UPDATE `$tb_user` SET kmoney=kmoney+'$money', kmaxmoney=kmaxmoney+'$money' WHERE userid='$userid'");

                // 记录资金变动
                userchange("提现申请被拒绝，退还资金：¥$money", $userid);
                echo 2;
            }

            // 提交事务
            $msql->query("COMMIT");

        } catch (Exception $e) {
            // 回滚事务
            $msql->query("ROLLBACK");
            echo 0;
        }
        break;

    case "detail":
        // 获取订单详情
        $id = intval($_GET['id']);
        if (!$id) {
            exit(json_encode(['code' => 0, 'msg' => '订单ID不能为空']));
        }

        $msql->query("SELECT * FROM `$tb_money` WHERE id='$id'");
        if (!$msql->next_record()) {
            exit(json_encode(['code' => 0, 'msg' => '订单不存在']));
        }

        $order_info = [
            'id' => $msql->f('id'),
            'orderid' => $msql->f('orderid'),
            'userid' => $msql->f('userid'),
            'mtype' => $msql->f('mtype'),
            'money' => $msql->f('money'),
            'sxfei' => $msql->f('sxfei'),
            'actual_amount' => $msql->f('actual_amount'),
            'payment_method' => $msql->f('payment_method'),
            'status' => $msql->f('status'),
            'bank' => $msql->f('bank'),
            'sname' => $msql->f('sname'),
            'snum' => $msql->f('snum'),
            'uname' => $msql->f('uname'),
            'unum' => $msql->f('unum'),
            'bank_branch' => $msql->f('bank_branch'),
            'alipay_account' => $msql->f('alipay_account'),
            'alipay_name' => $msql->f('alipay_name'),
            'alipay_qr_code' => $msql->f('alipay_qr_code'),
            'wechat_account' => $msql->f('wechat_account'),
            'wechat_name' => $msql->f('wechat_name'),
            'wechat_qr_code' => $msql->f('wechat_qr_code'),
            'usdt_amount' => $msql->f('usdt_amount'),
            'usdt_address' => $msql->f('usdt_address'),
            'usdt_network' => $msql->f('usdt_network'),
            'exchange_rate' => $msql->f('exchange_rate'),
            'proof_image' => $msql->f('proof_image'),
            'receiver_qr_code' => $msql->f('receiver_qr_code'),
            'tjtime' => $msql->f('tjtime'),
            'cltime' => $msql->f('cltime'),
            'admin_note' => $msql->f('admin_note'),
            'bz' => $msql->f('bz'),
            'ms' => $msql->f('ms')
        ];

        // 获取用户信息
        $fsql->query("SELECT username, tname FROM `$tb_user` WHERE userid='" . $msql->f('userid') . "'");
        if ($fsql->next_record()) {
            $order_info['username'] = $fsql->f('username');
            $order_info['tname'] = $fsql->f('tname');
        }

        exit(json_encode(['code' => 1, 'data' => $order_info]));
        break;

    case "user_info":
        // 获取用户绑定信息
        $userid = intval($_GET['userid']);
        if (!$userid) {
            exit(json_encode(['code' => 0, 'msg' => '用户ID不能为空']));
        }

        // 获取用户基本信息
        $msql->query("SELECT username, kmoney, regtime FROM `$tb_user` WHERE userid='$userid'");
        if (!$msql->next_record()) {
            exit(json_encode(['code' => 0, 'msg' => '用户不存在']));
        }

        $user_info = [
            'userid' => $userid,
            'username' => $msql->f('username'),
            'kmoney' => $msql->f('kmoney'),
            'regtime' => $msql->f('regtime'),
            'bank_info' => [],
            'alipay_info' => [],
            'wechat_info' => [],
            'usdt_info' => []
        ];

        // 获取银行卡信息
        $fsql->query("SELECT * FROM `$tb_banknum` WHERE userid='$userid' ORDER BY id DESC");
        while ($fsql->next_record()) {
            $user_info['bank_info'][] = [
                'bank' => transbank($fsql->f('bankid')),
                'account' => $fsql->f('num'),
                'name' => $fsql->f('name'),
                'branch' => $fsql->f('kaihuhang')
            ];
        }

        // 获取支付宝信息（从支付配置表获取）
        $fsql->query("SELECT config_value FROM `x_payment_config` WHERE payment_method='alipay' AND config_key='account_number'");
        if ($fsql->next_record()) {
            $user_info['alipay_info'][] = [
                'account' => $fsql->f('config_value'),
                'name' => '收款人姓名'
            ];
        }

        // 获取微信信息（从支付配置表获取）
        $fsql->query("SELECT config_value FROM `x_payment_config` WHERE payment_method='wechat' AND config_key='account_number'");
        if ($fsql->next_record()) {
            $user_info['wechat_info'][] = [
                'account' => $fsql->f('config_value'),
                'name' => '收款人姓名'
            ];
        }

        // 获取USDT信息（从支付配置表获取）
        $fsql->query("SELECT config_value FROM `x_payment_config` WHERE payment_method='usdt' AND config_key='wallet_address'");
        if ($fsql->next_record()) {
            $user_info['usdt_info'][] = [
                'address' => $fsql->f('config_value'),
                'network' => 'TRC20'
            ];
        }

        exit(json_encode(['code' => 1, 'data' => $user_info]));
        break;

    case "get_usdt_rate":
        // 获取当前USDT汇率
        include_once("usdt_rate.php");
        $rate = getCurrentUsdtRate();
        exit(json_encode(['code' => 1, 'rate' => $rate]));
        break;

    case "calculate_usdt":
        // USDT金额换算
        $usdt_amount = floatval($_GET['usdt_amount']);
        $cny_amount = floatval($_GET['cny_amount']);

        include_once("usdt_rate.php");
        $rate = getCurrentUsdtRate();

        if ($usdt_amount > 0) {
            // USDT转CNY
            $result_cny = $usdt_amount * $rate;
            exit(json_encode([
                'code' => 1,
                'cny_amount' => round($result_cny, 2),
                'rate' => $rate
            ]));
        } elseif ($cny_amount > 0) {
            // CNY转USDT
            $result_usdt = $cny_amount / $rate;
            exit(json_encode([
                'code' => 1,
                'usdt_amount' => round($result_usdt, 4),
                'rate' => $rate
            ]));
        } else {
            exit(json_encode(['code' => 0, 'msg' => '请输入有效金额']));
        }
        break;

    case "statistics":
        // 获取充值统计数据
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));

        // 今日统计 - 修复：充值应该是 mtype = 0
        $today_stats = [];
        $msql->query("SELECT
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as rejected_count,
            SUM(CASE WHEN status = 1 THEN money ELSE 0 END) as approved_amount,
            SUM(CASE WHEN status = 2 THEN money ELSE 0 END) as rejected_amount,
            SUM(money) as total_amount
            FROM `$tb_money`
            WHERE mtype = 0 AND DATE(tjtime) = '$today'");

        if ($msql->next_record()) {
            $today_stats = [
                'total_count' => intval($msql->f('total_count')),
                'pending_count' => intval($msql->f('pending_count')),
                'approved_count' => intval($msql->f('approved_count')),
                'rejected_count' => intval($msql->f('rejected_count')),
                'approved_amount' => floatval($msql->f('approved_amount')),
                'rejected_amount' => floatval($msql->f('rejected_amount')),
                'total_amount' => floatval($msql->f('total_amount'))
            ];
        } else {
            // 如果没有数据，返回默认值
            $today_stats = [
                'total_count' => 0,
                'pending_count' => 0,
                'approved_count' => 0,
                'rejected_count' => 0,
                'approved_amount' => 0.0,
                'rejected_amount' => 0.0,
                'total_amount' => 0.0
            ];
        }

        // 昨日统计（用于对比）- 修复：充值应该是 mtype = 0
        $yesterday_stats = [];
        $fsql->query("SELECT
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as rejected_count,
            SUM(CASE WHEN status = 1 THEN money ELSE 0 END) as approved_amount,
            SUM(CASE WHEN status = 2 THEN money ELSE 0 END) as rejected_amount,
            SUM(money) as total_amount
            FROM `$tb_money`
            WHERE mtype = 0 AND DATE(tjtime) = '$yesterday'");

        if ($fsql->next_record()) {
            $yesterday_stats = [
                'total_count' => intval($fsql->f('total_count')),
                'pending_count' => intval($fsql->f('pending_count')),
                'approved_count' => intval($fsql->f('approved_count')),
                'rejected_count' => intval($fsql->f('rejected_count')),
                'approved_amount' => floatval($fsql->f('approved_amount')),
                'rejected_amount' => floatval($fsql->f('rejected_amount')),
                'total_amount' => floatval($fsql->f('total_amount'))
            ];
        } else {
            // 如果没有昨日数据，返回默认值
            $yesterday_stats = [
                'total_count' => 0,
                'pending_count' => 0,
                'approved_count' => 0,
                'rejected_count' => 0,
                'approved_amount' => 0.0,
                'rejected_amount' => 0.0,
                'total_amount' => 0.0
            ];
        }

        // 计算变化百分比
        $changes = [];
        foreach ($today_stats as $key => $value) {
            $yesterday_value = isset($yesterday_stats[$key]) ? $yesterday_stats[$key] : 0;
            if ($yesterday_value > 0) {
                $change_percent = (($value - $yesterday_value) / $yesterday_value) * 100;
                $changes[$key] = round($change_percent, 1);
            } else {
                $changes[$key] = $value > 0 ? 100 : 0;
            }
        }

        $result = array_merge($today_stats, [
            'changes' => $changes,
            'yesterday_stats' => $yesterday_stats,
            'update_time' => date('Y-m-d H:i:s')
        ]);

        exit(json_encode(['code' => 1, 'data' => $result]));
        break;

    case "export":
        // 导出数据
        $status = isset($_GET['status']) ? intval($_GET['status']) : -1;
        $username = isset($_GET['username']) ? trim($_GET['username']) : '';
        $sdate = isset($_GET['sdate']) ? trim($_GET['sdate']) : '';
        $edate = isset($_GET['edate']) ? trim($_GET['edate']) : '';

        $where = "mtype = 1";

        if ($status >= 0) {
            $where .= " AND status = '$status'";
        }

        if ($username) {
            $fsql->query("SELECT userid FROM `$tb_user` WHERE username LIKE '%$username%' OR userid = '$username'");
            $userids = [];
            while ($fsql->next_record()) {
                $userids[] = $fsql->f('userid');
            }
            if (!empty($userids)) {
                $where .= " AND userid IN (" . implode(',', $userids) . ")";
            } else {
                $where .= " AND userid = 0"; // 没有找到用户
            }
        }

        if ($sdate) {
            $where .= " AND DATE(tjtime) >= '$sdate'";
        }

        if ($edate) {
            $where .= " AND DATE(tjtime) <= '$edate'";
        }

        // 设置导出头部
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="recharge_data_' . date('YmdHis') . '.xls"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr>';
        echo '<th>订单ID</th>';
        echo '<th>用户ID</th>';
        echo '<th>用户名</th>';
        echo '<th>充值金额</th>';
        echo '<th>手续费</th>';
        echo '<th>支付方式</th>';
        echo '<th>状态</th>';
        echo '<th>提交时间</th>';
        echo '<th>处理时间</th>';
        echo '</tr>';

        $msql->query("SELECT m.*, u.username FROM `$tb_money` m
                     LEFT JOIN `$tb_user` u ON m.userid = u.userid
                     WHERE $where ORDER BY m.tjtime DESC");

        while ($msql->next_record()) {
            echo '<tr>';
            echo '<td>' . $msql->f('id') . '</td>';
            echo '<td>' . $msql->f('userid') . '</td>';
            echo '<td>' . $msql->f('username') . '</td>';
            echo '<td>' . $msql->f('money') . '</td>';
            echo '<td>' . $msql->f('sxfei') . '</td>';
            echo '<td>' . ($msql->f('payment_method') ?: '银行转账') . '</td>';
            echo '<td>' . ($msql->f('status') == 0 ? '待处理' : ($msql->f('status') == 1 ? '已通过' : '已拒绝')) . '</td>';
            echo '<td>' . $msql->f('tjtime') . '</td>';
            echo '<td>' . $msql->f('cltime') . '</td>';
            echo '</tr>';
        }

        echo '</table>';
        exit;
        break;

    case "withdraw_statistics":
        // 获取提现统计数据
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));

        // 今日提现统计
        $today_stats = [];
        $msql->query("SELECT
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as rejected_count,
            SUM(CASE WHEN status = 1 THEN money ELSE 0 END) as approved_amount,
            SUM(CASE WHEN status = 2 THEN money ELSE 0 END) as rejected_amount,
            SUM(money) as total_amount
            FROM `$tb_money`
            WHERE mtype = 2 AND DATE(tjtime) = '$today'");

        if ($msql->next_record()) {
            $today_stats = [
                'total_count' => intval($msql->f('total_count')),
                'pending_count' => intval($msql->f('pending_count')),
                'approved_count' => intval($msql->f('approved_count')),
                'rejected_count' => intval($msql->f('rejected_count')),
                'approved_amount' => floatval($msql->f('approved_amount')),
                'rejected_amount' => floatval($msql->f('rejected_amount')),
                'total_amount' => floatval($msql->f('total_amount'))
            ];
        } else {
            // 如果没有数据，返回默认值
            $today_stats = [
                'total_count' => 0,
                'pending_count' => 0,
                'approved_count' => 0,
                'rejected_count' => 0,
                'approved_amount' => 0.0,
                'rejected_amount' => 0.0,
                'total_amount' => 0.0
            ];
        }

        // 昨日提现统计（用于对比）
        $yesterday_stats = [];
        $fsql->query("SELECT
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as rejected_count,
            SUM(CASE WHEN status = 1 THEN money ELSE 0 END) as approved_amount,
            SUM(CASE WHEN status = 2 THEN money ELSE 0 END) as rejected_amount,
            SUM(money) as total_amount
            FROM `$tb_money`
            WHERE mtype = 2 AND DATE(tjtime) = '$yesterday'");

        if ($fsql->next_record()) {
            $yesterday_stats = [
                'total_count' => intval($fsql->f('total_count')),
                'pending_count' => intval($fsql->f('pending_count')),
                'approved_count' => intval($fsql->f('approved_count')),
                'rejected_count' => intval($fsql->f('rejected_count')),
                'approved_amount' => floatval($fsql->f('approved_amount')),
                'rejected_amount' => floatval($fsql->f('rejected_amount')),
                'total_amount' => floatval($fsql->f('total_amount'))
            ];
        } else {
            // 如果没有昨日数据，返回默认值
            $yesterday_stats = [
                'total_count' => 0,
                'pending_count' => 0,
                'approved_count' => 0,
                'rejected_count' => 0,
                'approved_amount' => 0.0,
                'rejected_amount' => 0.0,
                'total_amount' => 0.0
            ];
        }

        // 计算变化百分比
        $changes = [];
        foreach ($today_stats as $key => $value) {
            $yesterday_value = isset($yesterday_stats[$key]) ? $yesterday_stats[$key] : 0;
            if ($yesterday_value > 0) {
                $change_percent = (($value - $yesterday_value) / $yesterday_value) * 100;
                $changes[$key] = round($change_percent, 1);
            } else {
                $changes[$key] = $value > 0 ? 100 : 0;
            }
        }

        $result = array_merge($today_stats, [
            'changes' => $changes,
            'yesterday_stats' => $yesterday_stats,
            'update_time' => date('Y-m-d H:i:s')
        ]);

        exit(json_encode(['code' => 1, 'data' => $result]));
        break;

    case "export_withdraw":
        // 导出提现数据
        $status = isset($_GET['status']) ? intval($_GET['status']) : -1;
        $username = isset($_GET['username']) ? trim($_GET['username']) : '';
        $sdate = isset($_GET['sdate']) ? trim($_GET['sdate']) : '';
        $edate = isset($_GET['edate']) ? trim($_GET['edate']) : '';

        $where = "mtype = 2";

        if ($status >= 0) {
            $where .= " AND status = '$status'";
        }

        if ($username) {
            $fsql->query("SELECT userid FROM `$tb_user` WHERE username LIKE '%$username%' OR userid = '$username'");
            $userids = [];
            while ($fsql->next_record()) {
                $userids[] = $fsql->f('userid');
            }
            if (!empty($userids)) {
                $where .= " AND userid IN (" . implode(',', $userids) . ")";
            } else {
                $where .= " AND userid = 0"; // 没有找到用户
            }
        }

        if ($sdate) {
            $where .= " AND DATE(tjtime) >= '$sdate'";
        }

        if ($edate) {
            $where .= " AND DATE(tjtime) <= '$edate'";
        }

        // 设置导出头部
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="withdraw_data_' . date('YmdHis') . '.xls"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr>';
        echo '<th>订单ID</th>';
        echo '<th>用户ID</th>';
        echo '<th>用户名</th>';
        echo '<th>提现金额</th>';
        echo '<th>手续费</th>';
        echo '<th>实际金额</th>';
        echo '<th>提现方式</th>';
        echo '<th>收款账户</th>';
        echo '<th>状态</th>';
        echo '<th>提交时间</th>';
        echo '<th>处理时间</th>';
        echo '</tr>';

        $msql->query("SELECT m.*, u.username FROM `$tb_money` m
                     LEFT JOIN `$tb_user` u ON m.userid = u.userid
                     WHERE $where ORDER BY m.tjtime DESC");

        while ($msql->next_record()) {
            $actual_amount = $msql->f('money') - $msql->f('sxfei');
            echo '<tr>';
            echo '<td>' . $msql->f('id') . '</td>';
            echo '<td>' . $msql->f('userid') . '</td>';
            echo '<td>' . $msql->f('username') . '</td>';
            echo '<td>' . $msql->f('money') . '</td>';
            echo '<td>' . $msql->f('sxfei') . '</td>';
            echo '<td>' . $actual_amount . '</td>';
            echo '<td>' . ($msql->f('payment_method') ?: '银行转账') . '</td>';
            echo '<td>' . $msql->f('sname') . ' - ' . $msql->f('snum') . '</td>';
            echo '<td>' . ($msql->f('status') == 0 ? '待处理' : ($msql->f('status') == 1 ? '已通过' : '已拒绝')) . '</td>';
            echo '<td>' . $msql->f('tjtime') . '</td>';
            echo '<td>' . $msql->f('cltime') . '</td>';
            echo '</tr>';
        }

        echo '</table>';
        exit;
        break;

    case "uptk":
        $id = $_POST['ids'];
        if (!is_numeric($id))
            exit;
        $sxfei   = $_POST['sxfei'];
        $ms      = $_POST['ms'];
        $bz      = $_POST['bz'];
        $cuntime = $_POST['cuntime'];
        $sql     = "update `$tb_money` set sxfei='$sxfei',ms='$ms',bz='$bz',cuntime='$cuntime' where id='$id'";
        if ($msql->query($sql)) {
            echo 1;
        }
        break;
    case "deltk":
        if ($_SESSION['hide'] != 1)
            exit;
        $id = explode('|', $_POST["idstr"]);
        foreach ($id as $v) {
            if (is_numeric($v) & $v != '') {
                $msql->query("delete from `$tb_money` where id='$v' and mtype=1");
            }
        }
        echo 1;
        break;
    case "uptkstatus":
        $id = $_POST['ids'];
        if (!is_numeric($id))
            exit;
        $status = $_POST["status"];
        if ($status != 1 & $status != 2)
            exit;

        // 获取提现订单信息
        $msql->query("SELECT userid, money, sxfei, status, mtype FROM `$tb_money` WHERE id='$id'");
        if (!$msql->next_record()) {
            echo 0;
            exit;
        }

        $userid = $msql->f('userid');
        $money = $msql->f('money');
        $sxfei = $msql->f('sxfei');
        $old_status = $msql->f('status');
        $mtype = $msql->f('mtype');

        // 只处理提现订单(mtype=2)且状态为待处理(status=0)的订单
        if ($mtype != 2 || $old_status != 0) {
            echo 0;
            exit;
        }

        // 开始事务
        $msql->query("START TRANSACTION");

        try {
            // 更新订单状态
            $msql->query("UPDATE `$tb_money` SET status='$status', cltime=NOW(), clid='$adminid' WHERE id='$id'");

            // 如果是拒绝提现(status=2)，需要退还资金给用户
            if ($status == 2) {
                // 退还提现金额给用户
                $fsql->query("UPDATE `$tb_user` SET kmoney=kmoney+'$money', kmaxmoney=kmaxmoney+'$money' WHERE userid='$userid'");

                // 记录资金变动
                $note = "提现申请被拒绝，退还资金：¥$money";
                $fsql->query("INSERT INTO `$tb_userchange` SET userid='$userid', content='$note', time=NOW()");
            }

            // 提交事务
            $msql->query("COMMIT");
            echo 1;

        } catch (Exception $e) {
            // 回滚事务
            $msql->query("ROLLBACK");
            echo 0;
        }
        break;
    case "notices":
        $username = trim($_REQUEST['username']);
        $upage    = $_REQUEST['upage'];
        $whi      = '';
        if ($username != '') {
            $whi .= " and userid in (select userid from `$tb_user` where username like '%$username%' or name like '%$username%' or tname like '%$username%' or userid='$username') ";
        }
        $sdate = rdates($_REQUEST['sdate']);
        $edate = rdates($_REQUEST['edate']);
        $tpl->assign("sdate", $sdate);
        $tpl->assign("edate", $edate);
        $s   = $sdate . " 00:00:00";
        $e   = $edate . " 23:59:59";
        $whi = " time>='$s' and time<='$e'  " . $whi;
        $msql->query("select count(id) from `$tb_notices` where $whi ");
        $msql->next_record();
        $rcount = $msql->f(0);
        $psize  = $config['psize1'];
        $upage  = r1p($upage);
        $pcount = $rcount % $psize == 0 ? $rcount / $psize : (($rcount - $rcount % $psize) / $psize + 1);
        if ($upage > $pcount)
            $upage = 1;
        if ($upage < 1)
            $upage = 1;
        $tpl->assign("rcount", $rcount);
        $tpl->assign("pcount", $pcount);
        $tpl->assign("upage", $upage);
        $msql->query("select * from `$tb_notices` where $whi order by time desc limit " . ($upage - 1) * $psize . "," . $psize);
        $notices = array();
        $i       = 0;
        while ($msql->next_record()) {
            $fsql->query("select username,tname from `$tb_user` where userid='" . $msql->f('userid') . "'");
            $fsql->next_record();
            $notices[$i]['username'] = $fsql->f("username");
            $notices[$i]['tname']    = $fsql->f("tname");
            $notices[$i]['title']    = $msql->f('title');
            $notices[$i]['time']     = $msql->f('time');
            $notices[$i]['du']       = $msql->f('du');
            $notices[$i]['content']  = $msql->f('content');
            $notices[$i]['id']       = $msql->f('id');
            $notices[$i]['userid']   = $msql->f('userid');
            if ($msql->f("sendid") == 99999999) {
                $notices[$i]['senduser'] = "系统";
            } else {
                $notices[$i]['senduser'] = transuser($msql->f('sendid'), 'username');
            }
            $i++;
        }
        $tpl->assign("notices", $notices);
        $tpl->display("money_notices.html");
        break;
    case "delnotices":
        $id = $_POST['id'];
        $id = explode('|', $id);
        foreach ($id as $v) {
            if ($v != '' & is_numeric($v)) {
                $msql->query("delete from `$tb_notices` where id='$v'");
            }
        }
        echo 1;
        break;
    case "sendmess":
        $uid = trim($_POST['uid']);
        if (!checkid($uid))
            exit;
        $title   = trim($_POST['title']);
        $content = $_POST['content'];
        $msql->query("insert into `$tb_notices` set userid='$uid',title='$title',du='$du',sendid='99999999',content='$content',time=NOW()");
        echo 1;
        break;
    case "xgmess":
        $uid = trim($_POST['uid']);
        if (!checkid($uid))
            exit;
        $title   = trim($_POST['title']);
        $content = $_POST['content'];
        $id      = trim($_POST['id']);
        $msql->query("update `$tb_notices` set content='$content',title='$title' where userid='$uid' and id='$id'");
        echo 1;
        break;
    case "tiquallmoney":
        $uid = $_POST['uid'];
        if (!checkfid($uid))
            exit;
        $etype = $_POST['etype'];
        $msql->query("select ifagent,layer,fudong,wid,kmoney,kmaxmoney from `$tb_user` where userid='$uid'");
        $msql->next_record();
        $ifagent = $msql->f('ifagent');
        $layer   = $msql->f('layer');
        $fudong  = $msql->f('fudong');
        $kmoney  = $msql->f('kmoney');
        $fsql->query("select moneytype from `$tb_web` where wid='" . $msql->f('wid') . "'");
        $fsql->next_record();
        if ($fsql->f('moneytype') == 0) {
            echo 2;
            exit;
        }
        if ($ifagent == 1) {
            if ($etype == 'slow') {
                $msql->query("update `$tb_user` set maxmoney=0,money=0 where  fid" . $layer . "='$uid'");
                $msql->query("update `$tb_user` set maxmoney=0,money=0 where userid='$uid'");
                $msql->query("select userid from `$tb_user` where  fid" . $layer . "='$uid'");
                while ($msql->next_record()) {
                    userchange("提取全部低频额度!", $msql->f('userid'));
                }
                userchange("提取全部低频额度!", $uid);
            } else if ($etype == 'fast') {
                $msql->query("update `$tb_user` set kmaxmoney=0,kmoney=0 where  fid" . $layer . "='$uid'");
                $msql->query("update `$tb_user` set kmaxmoney=0,kmoney=0 where userid='$uid'");
                $msql->query("select userid from `$tb_user` where  fid" . $layer . "='$uid'");
                while ($msql->next_record()) {
                    userchange("提取全部快开彩额度!", $msql->f('userid'));
                }
                userchange("提取全部快开彩额度!", $uid);
            }
        } else {
            if ($etype == 'slow') {
                $msql->query("update `$tb_user` set maxmoney=0,money=0 where userid='$uid'");
                userchange("提取全部低频彩额度!", $uid);
            } else if ($etype == 'fast') {
                $msql->query("update `$tb_user` set kmaxmoney=0,kmoney=0 where userid='$uid'");
                if ($fudong == 1) {
                    $msql->query("update `$tb_user` set kmaxmoney=0,kmoney=0,ftime=NOW() where userid='$uid'");
                    userchange("提取全部现金额度!", $uid);
                    $sql = "insert into `$tb_money` set userid='$uid',mtype=1,money='$kmoney',sxfei=0,cuntime=NOW(),status=1,tjid='$userid',tjtime=NOW(),clid='$userid',cltime=NOW()";
                    $msql->query($sql);
                } else {
                    $msql->query("update `$tb_user` set kmaxmoney=0,kmoney=0 where userid='$uid'");
                    userchange("提取全部快开彩额度!", $uid);
                }
            }
        }
        echo 1;
        break;
    case "setmoney":
        $uid = $_POST['uid'];
        if (!checkfid($uid))
            exit;
        $etype = $_POST['etype'];
        $je    = $_POST['je'];
        $types = $_POST['types'];
        if (!is_numeric($je) | $je % 1 != 0 | $je < 1)
            exit;
        if ($types != 0)
            $je = 0 - $je;
        $msql->query("select ifagent,layer,maxmoney,money,kmaxmoney,kmoney,fudong,fid,wid from `$tb_user` where userid='$uid'");
        $msql->next_record();
        $ifagent = $msql->f('ifagent');
        $layer   = $msql->f('layer');
        $fid     = $msql->f('fid');
        $fsql->query("select moneytype from `$tb_web` where wid='" . $msql->f('wid') . "'");
        $fsql->next_record();
        if ($fsql->f('moneytype') == 0) {
            echo 90;
            exit;
        }
        $kmaxmoney = $msql->f('kmaxmoney') + $je;
        $kmoney    = $msql->f('kmoney') + $je;
        if ($je < 0) {
            if ($msql->f('kmoney') < abs($je)) {
                echo 30;
                exit;
            }
            $sql = "update `$tb_user` set kmaxmoney='$kmoney',kmoney='$kmoney',ftime=NOW() where userid='$uid'";
            $fsql->query("select 1 from `$tb_lib` where userid='$uid' and z=9");
            $fsql->next_record();
            if ($fsql->f(0) == 1) {
                echo 32;
                exit;
            }
            $fsql->query($sql);
            userchange("提取快开彩额度".$je.",余额".($msql->f('kmoney') + $je),$uid);
            $sql = "insert into `$tb_money` set userid='$uid',mtype=1,money='$je',sxfei=0,cuntime=NOW(),status=1,tjid='$userid',tjtime=NOW(),clid='$userid',cltime=NOW()";
            $fsql->query($sql);
			echo 31;
			exit;
        } else {
            $sql = "update `$tb_user` set kmaxmoney='$kmoney',kmoney='$kmoney',ftime=NOW() where userid='$uid'";
            $fsql->query("select 1 from `$tb_lib` where userid='$uid' and z=9");
            $fsql->next_record();
            if ($fsql->f(0) == 1) {
                echo 42;
                exit;
            }
            $fsql->query($sql);
            userchange("存入快开彩额度".$je.",余额".($msql->f('kmoney')),$uid);
            $sql = "insert into `$tb_money` set userid='$uid',mtype=0,money='$je',sxfei=0,cuntime=NOW(),status=1,tjid='$userid',tjtime=NOW(),clid='$userid',cltime=NOW()";
            $fsql->query($sql);
            echo 41;
            exit;
        }
        break;
	case "getcztx":
	    $time  = sqltime(time()-86400);
	    $msql->query("select 1 from `$tb_money` where status=0 and tjtime>'$time'");
		$msql->next_record();
		if($msql->f(0)==1){
		   echo 1;
		}else {
		   echo 0;
		}
	break;
}
?>