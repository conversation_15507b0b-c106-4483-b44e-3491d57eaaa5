<?php
include('../data/comm.inc.php');
include('../data/myadminvar.php');
include('../func/func.php');
include('../func/adminfunc.php');
include('../include.php');
include('./checklogin.php');

switch ($_REQUEST['xtype']) {
    case "show":
        // 获取在线用户统计
        $msql->query("SELECT COUNT(DISTINCT userid) as online_users FROM x_customer_messages WHERE create_time > " . (time() - 1800)); // 30分钟内有消息的用户
        $msql->next_record();
        $online_users = $msql->f('online_users');
        
        // 获取未读消息统计
        $msql->query("SELECT COUNT(*) as unread_count FROM x_customer_messages WHERE sender_type='user' AND is_read=0");
        $msql->next_record();
        $unread_count = $msql->f('unread_count');
        
        // 获取今日消息统计
        $today_start = strtotime(date('Y-m-d 00:00:00'));
        $msql->query("SELECT COUNT(*) as today_messages FROM x_customer_messages WHERE create_time >= $today_start");
        $msql->next_record();
        $today_messages = $msql->f('today_messages');
        
        $tpl->assign("online_users", $online_users);
        $tpl->assign("unread_count", $unread_count);
        $tpl->assign("today_messages", $today_messages);
        $tpl->display("customer_chat.html");
        break;
        
    case 'get_active_users':
        // 获取活跃用户列表
        $limit = intval($_GET['limit']) ?: 20;
        
        $sql = "SELECT m.userid, u.username, 
                MAX(m.create_time) as last_message_time,
                COUNT(CASE WHEN m.sender_type='user' AND m.is_read=0 THEN 1 END) as unread_count,
                (SELECT message FROM x_customer_messages WHERE userid=m.userid ORDER BY create_time DESC LIMIT 1) as last_message
                FROM x_customer_messages m
                LEFT JOIN $tb_user u ON m.userid = u.userid
                WHERE m.create_time > " . (time() - 86400) . " 
                GROUP BY m.userid
                ORDER BY unread_count DESC, last_message_time DESC
                LIMIT $limit";
                
        $msql->query($sql);
        $users = array();
        while($msql->next_record()) {
            $users[] = array(
                'userid' => $msql->f('userid'),
                'username' => $msql->f('username'),
                'last_message_time' => $msql->f('last_message_time'),
                'unread_count' => $msql->f('unread_count'),
                'last_message' => $msql->f('last_message'),
                'last_message_formatted' => date('H:i', $msql->f('last_message_time'))
            );
        }

        exit(json_encode(array('code' => 1, 'data' => $users)));
        break;
        
    case 'get_conversation':
        // 获取对话记录
        $userid = intval($_GET['userid']);
        if(!$userid) {
            exit(json_encode(array('code' => 0, 'msg' => '用户ID不能为空')));
        }

        $msql->query("SELECT * FROM x_customer_messages WHERE userid='$userid' ORDER BY create_time ASC LIMIT 100");
        $messages = array();
        while($msql->next_record()) {
            $messages[] = array(
                'id' => $msql->f('id'),
                'message' => $msql->f('message'),
                'message_type' => $msql->f('message_type'),
                'image_url' => $msql->f('image_url'),
                'sender_type' => $msql->f('sender_type'),
                'is_read' => $msql->f('is_read'),
                'tjtime' => $msql->f('tjtime'),
                'create_time' => $msql->f('create_time'),
            );
        }

        // 获取用户信息
        $msql->query("SELECT username, kmoney FROM $tb_user WHERE userid='$userid'");
        $user_info = null;
        if($msql->next_record()) {
            $user_info = array(
                'username' => $msql->f('username'),
                'balance' => $msql->f('kmoney')
            );
        }

        // 标记消息为已读
        $msql->query("UPDATE x_customer_messages SET is_read=1 WHERE userid='$userid' AND sender_type='user' AND is_read=0");

        exit(json_encode(array('code' => 1, 'data' => array('messages' => $messages, 'user_info' => $user_info))));
        break;
        
    case 'send_message':
        // 发送消息
        $result = array('code' => 1, 'msg' => '发送成功');
        try {
            $userid = intval($_POST['userid']);
            $message = trim($_POST['message']);
            $message_type = 'text';

            if(!$userid) throw new Exception('用户ID不能为空');
            if(empty($message)) throw new Exception('消息内容不能为空');

            $time = time();
            $tjtime = date('Y-m-d H:i:s');

            // 插入消息记录
            $sql = "INSERT INTO x_customer_messages SET
                userid='$userid',
                message='$message',
                message_type='$message_type',
                sender_type='admin',
                is_read=0,
                tjtime='$tjtime',
                create_time='$time'";

            if(!$msql->query($sql)) {
                throw new Exception('发送失败');
            }

            $result['data'] = array(
                'id' => $msql->insert_id(),
                'message' => $message,
                'message_type' => $message_type,
                'sender_type' => 'admin',
                'tjtime' => $tjtime,
                'create_time' => $time
            );

        } catch (Exception $e) {
            $result['code'] = 0;
            $result['msg'] = $e->getMessage();
        }
        exit(json_encode($result));
        break;

    case 'upload_image':
        // 图片上传
        $result = array('code' => 1, 'msg' => '上传成功');
        try {
            if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('图片上传失败');
            }

            $file = $_FILES['image'];
            $allowedTypes = array('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp');

            if (!in_array($file['type'], $allowedTypes)) {
                throw new Exception('不支持的图片格式');
            }

            if ($file['size'] > 5 * 1024 * 1024) { // 5MB
                throw new Exception('图片大小不能超过5MB');
            }

            // 创建上传目录
            $uploadDir = '../uploads/customer/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // 生成文件名
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = date('YmdHis') . '_' . uniqid() . '.' . $extension;
            $filepath = $uploadDir . $filename;

            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception('文件保存失败');
            }

            $result['data'] = array(
                'image_url' => '/uploads/customer/' . $filename,
                'filename' => $filename
            );

        } catch (Exception $e) {
            $result['code'] = 0;
            $result['msg'] = $e->getMessage();
        }
        exit(json_encode($result));
        break;

    case 'get_stats':
        // 获取实时统计
        $stats = array();

        // 在线用户数
        $msql->query("SELECT COUNT(DISTINCT userid) as count FROM x_customer_messages WHERE create_time > " . (time() - 1800));
        $msql->next_record();
        $stats['online_users'] = $msql->f('count');

        // 未读消息数
        $msql->query("SELECT COUNT(*) as count FROM x_customer_messages WHERE sender_type='user' AND is_read=0");
        $msql->next_record();
        $stats['unread_messages'] = $msql->f('count');

        // 今日消息数
        $today_start = strtotime(date('Y-m-d 00:00:00'));
        $msql->query("SELECT COUNT(*) as count FROM x_customer_messages WHERE create_time >= $today_start");
        $msql->next_record();
        $stats['today_messages'] = $msql->f('count');

        // 待处理用户数
        $msql->query("SELECT COUNT(DISTINCT userid) as count FROM x_customer_messages WHERE sender_type='user' AND is_read=0");
        $msql->next_record();
        $stats['pending_users'] = $msql->f('count');

        exit(json_encode(array('code' => 1, 'data' => $stats)));
        break;
        
    case 'quick_reply':
        // 快速回复
        $result = array('code' => 1, 'msg' => '发送成功');
        try {
            $userid = intval($_POST['userid']);
            $template_id = intval($_POST['template_id']);

            if(!$userid) throw new Exception('用户ID不能为空');

            // 预设回复模板
            $templates = array(
                1 => '您好，欢迎咨询！我是客服，有什么可以帮助您的吗？',
                2 => '请稍等，我正在为您查询相关信息...',
                3 => '感谢您的咨询，如有其他问题请随时联系我们。',
                4 => '抱歉让您久等了，请问还有什么需要帮助的吗？',
                5 => '您的问题我已经记录，会尽快为您处理。',
            );

            $message = isset($templates[$template_id]) ? $templates[$template_id] : '';
            if(empty($message)) throw new Exception('模板不存在');

            $time = time();
            $tjtime = date('Y-m-d H:i:s');

            // 插入消息记录
            $sql = "INSERT INTO x_customer_messages SET
                userid='$userid',
                message='$message',
                message_type='text',
                sender_type='admin',
                is_read=0,
                tjtime='$tjtime',
                create_time='$time'";

            if(!$msql->query($sql)) {
                throw new Exception('发送失败');
            }

            $result['data'] = array(
                'id' => $msql->insert_id(),
                'message' => $message,
                'message_type' => 'text',
                'sender_type' => 'admin',
                'tjtime' => $tjtime,
                'create_time' => $time
            );

        } catch (Exception $e) {
            $result['code'] = 0;
            $result['msg'] = $e->getMessage();
        }
        exit(json_encode($result));
        break;
}
?>
