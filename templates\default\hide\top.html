<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>管理后台 - {+$webname+}</title>
<link href="/css/default/jquery-ui.css" rel="stylesheet" type="text/css" />
<link href="/css/default/master.css?v=2126" rel="stylesheet" type="text/css" />
<link href="/css/default/layout.css?v=1.6" rel="stylesheet" type="text/css" />
<link href="/css/default/httop.css?v=1.2" rel="stylesheet" type="text/css" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<script language="javascript" src="/js/jquery-1.8.3.min.js"></script>
<script language="javascript" src="/js/jquery-1.11.3.js"></script>
<script language="javascript" src="/js/ui/jquery-ui.js"></script>
<script language="javascript">
function hideinfo(){ if(event.srcElement.tagName=="A"){
   window.status=event.srcElement.innerText}
}
document.onmouseover=hideinfo; 
document.onmousemove=hideinfo;
var globalpath = "{+$globalpath+}";  
function toggleSubMenu(element) {  
    var gid = element.getAttribute('gid'); // 获取当前游戏的 gid  
    var subMenuId = 'submenu_' + gid; // 构建子菜单的 id  
    var subMenu = document.getElementById(subMenuId); // 获取子菜单元素  
    if (subMenu.style.display === 'none' || subMenu.style.display === '') {  
        subMenu.style.display = 'block'; // 显示子菜单  
    } else {  
        subMenu.style.display = 'none'; // 隐藏子菜单  
    }  
} 
document.addEventListener('DOMContentLoaded', function() {  
    var scrollText = document.getElementById('notices');  
  
    // 鼠标移入时暂停滚动  
    scrollText.addEventListener('mouseenter', function() {  
        this.classList.add('paused');  
    });  
  
    // 鼠标移出时恢复滚动  
    scrollText.addEventListener('mouseleave', function() {  
        this.classList.remove('paused');  
    });  
  
    // 可选：点击时也可以暂停或恢复滚动（取决于您的需求）  
    scrollText.addEventListener('click', function() {  
        // 这里可以根据需要切换paused类，或者执行其他操作  
        // 例如，如果点击时您只想暂停而不恢复，那么您可能不需要这个事件监听器  
        // 但如果您想要一个切换效果，就像之前的示例那样，那么您应该保留它  
        // this.classList.toggle('paused');  
    });  
});
</script>
<link href="/css/default/ball.css" rel="stylesheet" type="text/css" />
</head>
<body id="topbody" class="modern-admin">
<script id=myjs language="javascript">var mulu='{+$mulu+}';var js=1;var sss='top';</script>

<!-- 现代化顶部导航栏 -->
<header class="modern-header">
    <div class="header-container">
        <!-- Logo区域 -->
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="logo-text">
                <h1>{+$webname+}</h1>
                <span>管理后台</span>
            </div>
        </div>

        <!-- 游戏选择器 -->
        <div class="game-selector">
            <div class="dropdown">
                <button class="dropdown-toggle" id="gameDropdown">
                    <i class="fas fa-gamepad"></i>
                    <span>选择游戏</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <ul class="dropdown-menu" id="gameMenu">
                    {+section name=i loop=$gamecs+}
                    <li>
                        <a href="javascript:void(0)"
                           gid="{+$gamecs[i].gid+}"
                           gname="{+$gamecs[i].gname+}"
                           fenlei="{+$gamecs[i].fenlei+}"
                           {+if $gid == $gamecs[i].gid+}class="active"{+/if+}>
                            <i class="fas fa-dice"></i>
                            {+$gamecs[i].gname+}
                        </a>
                    </li>
                    {+/section+}
                </ul>
            </div>
        </div>

        <!-- 状态信息卡片 -->
        <div class="status-cards">
            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="status-info">
                    <div class="status-label">当前期数</div>
                    <div class="status-value red-text">{+$qishu+}</div>
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-stopwatch"></i>
                </div>
                <div class="status-info">
                    <div class="status-label">{+if $panstatus==1+}距关盘{+else+}距开盘{+/if+}</div>
                    <div class="status-value red-text time0">{+$pantime+}</div>
                </div>
            </div>

            {+if $gid==100+}
            <div class="status-card otherstatus {+if $otherstatus==0+}hide{+/if+}">
                <div class="status-icon">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <div class="status-info">
                    <div class="status-label">{+if $otherstatus==1+}距正码关盘{+else+}距正码开盘{+/if+}</div>
                    <div class="status-value red-text time1">{+$othertime+}</div>
                </div>
            </div>
            {+/if+}

            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="status-info">
                    <div class="status-label">上期开奖</div>
                    <div class="status-value red-text upqishu" m='{+$upkj+}'>{+$upqishu+}</div>
                </div>
            </div>
        </div>

        <!-- 用户信息和操作 -->
        <div class="user-section">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">{+$name+}</div>
                    <div class="user-role">管理员</div>
                </div>
            </div>

            <div class="header-actions">
                <button class="action-btn qzclose" title="关盘操作">
                    <i class="fas fa-stop-circle"></i>
                    <span>关盘</span>
                </button>

                <a href="/hide/customer_manage.php?xtype=show" target="frame" class="action-btn" id="customerServiceLink" title="客服消息">
                    <i class="fas fa-comments"></i>
                    <span>客服</span>
                    <span id="unreadBadge" class="notification-badge" style="display: none;">0</span>
                </a>

                <a href="/hide/online.php?xtype=show" target="frame" class="action-btn" title="在线会员">
                    <i class="fas fa-users"></i>
                    <span class="online">{+$onlinenum+}</span>
                </a>
            </div>
        </div>
    </div>
</header>
<!-- 现代化主导航菜单 -->
<nav class="main-navigation">
    <div class="nav-container">
        <div class="nav-grid">
            <!-- 即时注单 -->
            {+if $slib==1+}
            <div class="nav-card" data-category="betting">
                <a href="javascript:void(0);" class="nav-link lib control" i=0 x="slib">
                    <div class="nav-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="nav-content">
                        <h3>即时注单</h3>
                        <p>实时投注管理</p>
                    </div>
                </a>
            </div>
            {+/if+}

            <!-- 开奖管理 -->
            {+if $kj==1+}
            <div class="nav-card" data-category="lottery">
                <a href="javascript:void(0);" target="frame" x="kj" type='show' class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="nav-content">
                        <h3>开奖管理</h3>
                        <p>开奖结果控制</p>
                    </div>
                </a>
            </div>
            {+/if+}

            <!-- 用户管理 -->
            {+if $suser==1+}
            <div class="nav-card" data-category="users">
                <a href="javascript:void(0);" x='suser' i=1 class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="nav-content">
                        <h3>用户管理</h3>
                        <p>会员信息管理</p>
                    </div>
                </a>
            </div>
            {+/if+}

            <!-- 注单管理 -->
            <div class="nav-card" data-category="betting">
                <a href="javascript:void(0)" target="frame" x='now' type='show' class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-list-alt"></i>
                    </div>
                    <div class="nav-content">
                        <h3>注单管理</h3>
                        <p>投注记录查看</p>
                    </div>
                </a>
            </div>

            <!-- 报表查询 -->
            {+if $baox==1+}
            <div class="nav-card" data-category="reports">
                <a href="javascript:void(0);" target="frame" x="baox" class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="nav-content">
                        <h3>报表查询</h3>
                        <p>数据统计分析</p>
                    </div>
                </a>
            </div>
            {+/if+}

            <!-- 开奖结果 -->
            <div class="nav-card" data-category="lottery">
                <a href="javascript:void(0);" target="frame" x="longs" class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="nav-content">
                        <h3>开奖结果</h3>
                        <p>历史开奖记录</p>
                    </div>
                </a>
            </div>

            <!-- 系统功能 -->
            <div class="nav-card" data-category="system">
                <a href="javascript:void(0);" target="frame" i=3 x="caopan" class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="nav-content">
                        <h3>系统功能</h3>
                        <p>系统设置管理</p>
                    </div>
                </a>
            </div>

            <!-- 高级功能 -->
            {+if $hide==1+}
            <div class="nav-card" data-category="advanced">
                <a href="javascript:void(0);" target="frame" i=5 x="check" class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="nav-content">
                        <h3>高级功能</h3>
                        <p>高级管理工具</p>
                    </div>
                </a>
            </div>
            {+/if+}

            <!-- 现金管理 -->
            <div class="nav-card {+if $money!=1+}nav-disabled{+/if+}" data-category="finance">
                <a href="javascript:void(0);" target="frame" i=4 class='xjgl nav-link' x="money" {+if $money!=1+}style='pointer-events:none;opacity:0.5;'{+/if+}>
                    <div class="nav-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="nav-content">
                        <h3>现金管理</h3>
                        <p>充值提现管理</p>
                    </div>
                </a>
            </div>

            <!-- 客服管理 -->
            <div class="nav-card" data-category="service">
                <a href="javascript:void(0);" target="frame" x="customer_index" class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="nav-content">
                        <h3>客服管理</h3>
                        <p>客户服务支持</p>
                    </div>
                </a>
            </div>

            <!-- 支付配置 -->
            <div class="nav-card" data-category="finance">
                <a href="javascript:void(0);" target="frame" x="payment_config" class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="nav-content">
                        <h3>支付配置</h3>
                        <p>支付方式设置</p>
                    </div>
                </a>
            </div>

            <!-- 密码修改 -->
            <div class="nav-card" data-category="security">
                <a href="javascript:void(0);" target="frame" x="changepass2" class="nav-link">
                    <div class="nav-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="nav-content">
                        <h3>密码修改</h3>
                        <p>安全设置管理</p>
                    </div>
                </a>
            </div>

            <!-- 退出系统 -->
            <div class="nav-card logout-card" data-category="system">
                <a href="javascript:void(0);" class="nav-link logout-link">
                    <div class="nav-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <div class="nav-content">
                        <h3>退出系统</h3>
                        <p>安全退出登录</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</nav>


<!-- 子菜单区域 - 系统功能 -->
<div class="submenu-section" id="systemSubmenu" style="display: none;">
    <div class="submenu-container">
        <div class="submenu-header">
            <h3><i class="fas fa-cogs"></i> 系统功能</h3>
            <p>系统管理和配置工具</p>
        </div>
        <div class="submenu-grid">
            {+if $buhuo==1+}
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u="fly" type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>飞单跟投设置</h4>
                        <p>配置跟投参数</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u="fly" type='flylist' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>飞单记录</h4>
                        <p>查看飞单历史</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='libset' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-limit"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>飞单限额</h4>
                        <p>设置投注限额</p>
                    </div>
                </a>
            </div>
            {+/if+}

            {+if $liushui==1+}
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u="fly" type='shui' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>赚分设置</h4>
                        <p>配置分成比例</p>
                    </div>
                </a>
            </div>
            {+/if+}

            {+if $libset==1+}
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='libset' type='warn' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>警示金额</h4>
                        <p>设置警告阈值</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='libset' type='auto' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>自动降倍</h4>
                        <p>自动调整倍数</p>
                    </div>
                </a>
            </div>
            {+/if+}

            {+if $zshui==1+}
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='zshui' type='ma' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>号码属性</h4>
                        <p>设置号码特性</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='zshui' type='ptype' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>默认赔率</h4>
                        <p>配置赔率设置</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='zshui' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>默认退水</h4>
                        <p>设置退水比例</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='zshui' type='setattshow' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-sliders-h"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>赔率参数</h4>
                        <p>调整赔率参数</p>
                    </div>
                </a>
            </div>
            {+/if+}

            {+if $news==1+}
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='news' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>消息管理</h4>
                        <p>系统消息设置</p>
                    </div>
                </a>
            </div>
            {+/if+}

            {+if ($hide==1 || ($caopan==1 && $xxtz2==1))+}
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='zdcx' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>注单删改</h4>
                        <p>修改投注记录</p>
                    </div>
                </a>
            </div>
            {+/if+}

            {+if ($hide==1 | $caopan==1)+}
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='caopan' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>系统管理员</h4>
                        <p>管理员设置</p>
                    </div>
                </a>
            </div>

            {+if $err==1+}
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='err' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-bug"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>异常注单</h4>
                        <p>处理异常订单</p>
                    </div>
                </a>
            </div>
            {+/if+}

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='history' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-archive"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>记录管理</h4>
                        <p>历史记录查看</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='sysconfig' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>系统参数</h4>
                        <p>基础参数配置</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='online' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>在线状态</h4>
                        <p>查看在线情况</p>
                    </div>
                </a>
            </div>
            {+/if+}
        </div>
    </div>
</div>
    
<!-- 子菜单区域 - 现金管理 -->
<div class="submenu-section" id="moneySubmenu" style="display: {+if $money!=1+}none{+else+}none{+/if+};">
    <div class="submenu-container">
        <div class="submenu-header">
            <h3><i class="fas fa-wallet"></i> 现金管理</h3>
            <p>充值提现和资金管理</p>
        </div>
        <div class="submenu-grid">
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='money' type='moneyuser' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>现金会员</h4>
                        <p>会员资金管理</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='money' type='chongzhi' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>充值管理</h4>
                        <p>处理充值申请</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='money' type='tikuan' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-minus-circle"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>提现管理</h4>
                        <p>处理提现申请</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" onclick="openUsdtRate()" class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>USDT汇率管理</h4>
                        <p>汇率设置和管理</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='money' type='bank' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>银行管理</h4>
                        <p>银行信息设置</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='money' type='chongzhifs' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>充值方式</h4>
                        <p>配置充值渠道</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='money' type='banknum' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-piggy-bank"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>收款账户</h4>
                        <p>管理收款信息</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='money' type='notices' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>消息管理</h4>
                        <p>系统通知设置</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>


<!-- 子菜单区域 - 高级功能 -->
{+if $hide==1+}
<div class="submenu-section" id="advancedSubmenu" style="display: none;">
    <div class="submenu-container">
        <div class="submenu-header">
            <h3><i class="fas fa-tools"></i> 高级功能</h3>
            <p>高级管理和配置工具</p>
        </div>
        <div class="submenu-grid">
            <div class="submenu-card">
                <a href="javascript:void(0);" target="frame" u="baox" type='oldshow' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>报表查询</h4>
                        <p>详细数据报表</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='xxtz' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>注单明细</h4>
                        <p>详细投注信息</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='now' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>注单管理</h4>
                        <p>投注订单管理</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='webconfig' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-globe-americas"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>网站配置</h4>
                        <p>网站基础设置</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='game' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>游戏配置</h4>
                        <p>游戏参数设置</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='zshui' type='gameset' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-toggle-on"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>彩种开放</h4>
                        <p>彩票种类管理</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='class' type='classpan' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>玩法归类</h4>
                        <p>玩法分类管理</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='class' type='bigclass' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>大分类</h4>
                        <p>主要分类设置</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='class' type='sclass' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>小分类</h4>
                        <p>细分类别管理</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='class' type='class' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>玩法分类</h4>
                        <p>玩法类型设置</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='play' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-list-ul"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>玩法列表</h4>
                        <p>所有玩法管理</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='err' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>异常注单</h4>
                        <p>异常订单处理</p>
                    </div>
                </a>
            </div>

            {+if $hides==1+}
            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='check' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>系统检测</h4>
                        <p>系统状态检查</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='message' type='show' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-comment-dots"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>会员反馈</h4>
                        <p>用户意见管理</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='play' type='downlist' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>下载记录</h4>
                        <p>文件下载历史</p>
                    </div>
                </a>
            </div>

            <div class="submenu-card">
                <a href="javascript:void(0)" target="frame" u='loglist' type='loglist' class="submenu-link">
                    <div class="submenu-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="submenu-content">
                        <h4>注单记录</h4>
                        <p>投注日志查看</p>
                    </div>
                </a>
            </div>
            {+/if+}
        </div>
    </div>
</div>
{+/if+}

<!-- 主内容区域 -->
<main class="main-content">
    <div class="content-container">
        <iframe id="frame" name="frame" src='/hide/new.php' frameborder="0" class="content-frame"></iframe>
    </div>
</main>
<!-- 现代化底部区域 -->
<footer class="modern-footer">
    <div class="footer-container">
        <div class="footer-content">
            <div class="footer-section">
                <div class="notice-area">
                    <div class="notice-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <div class="notice-content">
                        <div class="notice-label">系统公告</div>
                        <div class="notice-text">
                            <span id="notices" class="scroll-text">这是一条会滚动的消息，它将从左向右持续滚动。</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-actions">
                <button class="footer-btn" onclick="toggleNoticeSettings()">
                    <i class="fas fa-cog"></i>
                    <span>公告设置</span>
                </button>

                <a href="javascript:void(0);" class="footer-btn more-btn" target="frame">
                    <i class="fas fa-ellipsis-h"></i>
                    <span>更多功能</span>
                </a>
            </div>
        </div>
    </div>
</footer>

<div id="dialog" title="您有新的交易请求" style="display:none;">
  <p style="text-align:center"><button class="clqq s1">前往处理</button></p>
</div>

<!-- 客服悬浮组件 -->
<div id="customerServiceFloat" class="customer-service-float">
    <div class="float-main" id="floatMain">
        <div class="float-icon">
            <i class="service-icon">💬</i>
            <span class="float-badge" id="floatBadge" style="display: none;">0</span>
        </div>
        <div class="float-text">客服</div>
    </div>

    <!-- 展开的功能面板 -->
    <div class="float-panel" id="floatPanel" style="display: none;">
        <div class="panel-header">
            <span class="panel-title">客服中心</span>
            <button class="panel-close" onclick="toggleFloatPanel()">&times;</button>
        </div>
        <div class="panel-content">
            <div class="service-item" onclick="openCustomerChat()">
                <div class="item-icon chat-icon">💬</div>
                <div class="item-info">
                    <div class="item-title">客服聊天</div>
                    <div class="item-desc">实时消息管理</div>
                </div>
                <div class="item-badge" id="chatBadge" style="display: none;">0</div>
            </div>

            <div class="service-item" onclick="openCustomerManage()">
                <div class="item-icon manage-icon">👥</div>
                <div class="item-info">
                    <div class="item-title">客服管理</div>
                    <div class="item-desc">用户消息管理</div>
                </div>
            </div>

            <div class="service-item" onclick="openPaymentConfig()">
                <div class="item-icon payment-icon">💳</div>
                <div class="item-info">
                    <div class="item-title">支付配置</div>
                    <div class="item-desc">支付方式设置</div>
                </div>
            </div>

            <div class="service-item" onclick="openUsdtRate()">
                <div class="item-icon rate-icon">💰</div>
                <div class="item-info">
                    <div class="item-title">汇率管理</div>
                    <div class="item-desc">USDT汇率设置</div>
                </div>
            </div>

            <div class="service-item" onclick="openWithdrawManage()">
                <div class="item-icon withdraw-icon">📤</div>
                <div class="item-info">
                    <div class="item-title">提现管理</div>
                    <div class="item-desc">提现审核处理</div>
                </div>
            </div>

            <div class="service-item" onclick="openRechargeManage()">
                <div class="item-icon recharge-icon">📥</div>
                <div class="item-info">
                    <div class="item-title">充值管理</div>
                    <div class="item-desc">充值记录管理</div>
                </div>
            </div>
        </div>
    </div>
</div>
<script language="javascript" id='zhishu'>
var ngid={+$gid+};
var fenlei = {+$fenlei+};
var layer={+$layer+};
ma = [];
     ma['紅'] = new Array(01,02,07,08,12,13,18,19,23,24,29,30,34,35,40,45,46);
     ma['藍'] = new Array(03,04,09,10,14,15,20,25,26,31,36,37,41,42,47,48);
     ma['綠'] = new Array(05,06,11,16,17,21,22,27,28,32,33,38,39,43,44,49);

// 客服消息检查
function checkUnreadMessages() {
    $.ajax({
        type: 'GET',
        url: mulu + 'customer_manage.php?xtype=messages&unread_only=1&limit=1',
        dataType: 'json',
        cache: false,
        success: function(res) {
            if (res && res.code === 1 && res.data) {
                var unreadCount = res.data.unread_count || 0;

                var badge = document.getElementById('unreadBadge');
                if (badge) {
                    if (unreadCount > 0) {
                        badge.style.display = 'inline-block';
                        badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                        // 添加闪烁效果
                        badge.style.animation = 'blink 1s infinite';
                    } else {
                        badge.style.display = 'none';
                        badge.style.animation = 'none';
                    }
                }
            }
        },
        error: function() {
            // 静默处理错误
        }
    });
}

// 页面加载完成后开始检查
$(document).ready(function() {
    // 立即检查一次
    checkUnreadMessages();

    // 每30秒检查一次未读消息
    setInterval(checkUnreadMessages, 30000);

    // 初始化客服悬浮组件
    initCustomerServiceFloat();
});

// 客服悬浮组件功能
let isDragging = false;
let dragOffset = { x: 0, y: 0 };
let isPanelOpen = false;

function initCustomerServiceFloat() {
    const floatElement = document.getElementById('customerServiceFloat');
    const floatMain = document.getElementById('floatMain');
    const floatPanel = document.getElementById('floatPanel');

    if (!floatElement || !floatMain) return;

    // 从localStorage恢复位置
    const savedPosition = localStorage.getItem('customerFloatPosition');
    if (savedPosition) {
        const position = JSON.parse(savedPosition);
        floatElement.style.bottom = position.bottom + 'px';
        floatElement.style.right = position.right + 'px';
    }

    // 鼠标按下事件
    floatMain.addEventListener('mousedown', function(e) {
        e.preventDefault();
        isDragging = true;

        const rect = floatElement.getBoundingClientRect();
        dragOffset.x = e.clientX - rect.left;
        dragOffset.y = e.clientY - rect.top;

        floatElement.classList.add('dragging');

        // 如果面板是打开的，先关闭它
        if (isPanelOpen) {
            floatPanel.style.display = 'none';
            isPanelOpen = false;
        }
    });

    // 鼠标移动事件
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        e.preventDefault();

        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementWidth = floatElement.offsetWidth;
        const elementHeight = floatElement.offsetHeight;

        let newX = windowWidth - (e.clientX - dragOffset.x + elementWidth);
        let newY = windowHeight - (e.clientY - dragOffset.y + elementHeight);

        // 边界限制
        newX = Math.max(10, Math.min(newX, windowWidth - elementWidth - 10));
        newY = Math.max(10, Math.min(newY, windowHeight - elementHeight - 10));

        floatElement.style.right = newX + 'px';
        floatElement.style.bottom = newY + 'px';
    });

    // 鼠标释放事件
    document.addEventListener('mouseup', function(e) {
        if (!isDragging) return;

        isDragging = false;
        floatElement.classList.remove('dragging');

        // 保存位置到localStorage
        const rect = floatElement.getBoundingClientRect();
        const position = {
            right: window.innerWidth - rect.right,
            bottom: window.innerHeight - rect.bottom
        };
        localStorage.setItem('customerFloatPosition', JSON.stringify(position));
    });

    // 点击事件（非拖拽时）
    floatMain.addEventListener('click', function(e) {
        if (!isDragging) {
            toggleFloatPanel();
        }
    });

    // 点击外部关闭面板
    document.addEventListener('click', function(e) {
        if (!floatElement.contains(e.target) && isPanelOpen) {
            floatPanel.style.display = 'none';
            isPanelOpen = false;
        }
    });

    // 更新悬浮组件的未读消息数量
    updateFloatBadge();
    setInterval(updateFloatBadge, 30000);
}

function toggleFloatPanel() {
    const floatPanel = document.getElementById('floatPanel');

    if (isPanelOpen) {
        floatPanel.style.display = 'none';
        isPanelOpen = false;
    } else {
        floatPanel.style.display = 'block';
        isPanelOpen = true;
    }
}

function updateFloatBadge() {
    const floatBadge = document.getElementById('floatBadge');
    const chatBadge = document.getElementById('chatBadge');
    const unreadBadge = document.getElementById('unreadBadge');

    // 从现有的未读消息数量获取
    if (unreadBadge && unreadBadge.style.display !== 'none') {
        const count = unreadBadge.textContent;
        if (floatBadge) {
            floatBadge.textContent = count;
            floatBadge.style.display = 'inline-block';
        }
        if (chatBadge) {
            chatBadge.textContent = count;
            chatBadge.style.display = 'inline-block';
        }
    } else {
        if (floatBadge) floatBadge.style.display = 'none';
        if (chatBadge) chatBadge.style.display = 'none';
    }
}

// 各个功能页面跳转函数
function openCustomerChat() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/customer_chat.php?xtype=show';
    }
    toggleFloatPanel();
}

function openCustomerManage() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/customer_manage.php?xtype=show';
    }
    toggleFloatPanel();
}

function openPaymentConfig() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/payment_config.php?xtype=show';
    }
    toggleFloatPanel();
}

function openUsdtRate() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/usdt_rate.php?xtype=show';
    }
    toggleFloatPanel();
}

function openWithdrawManage() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/withdraw.php?xtype=show';
    }
    toggleFloatPanel();
}

function openRechargeManage() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/recharge.php?xtype=show';
    }
    toggleFloatPanel();
}

</script>

<style>
/* 现代化管理后台样式 */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #4CAF50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196F3;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --border-color: #e0e6ed;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重置和基础样式 */
.modern-admin {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: var(--dark-color);
}

.modern-admin * {
    box-sizing: border-box;
}

/* 现代化顶部导航栏 */
.modern-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: var(--shadow-medium);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
    gap: 2rem;
}

/* Logo区域 */
.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.logo-text h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.logo-text span {
    font-size: 0.875rem;
    opacity: 0.8;
    display: block;
    margin-top: 2px;
}

/* 游戏选择器 */
.game-selector {
    position: relative;
}

.dropdown-toggle {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    font-size: 0.875rem;
    min-width: 150px;
}

.dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    margin-top: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
}

.dropdown-menu a:hover,
.dropdown-menu a.active {
    background: var(--light-color);
    color: var(--primary-color);
}

/* 状态信息卡片 */
.status-cards {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.status-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    padding: 1rem;
    min-width: 120px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: var(--transition);
}

.status-card:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.status-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
}

.status-info {
    color: white;
}

.status-label {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-bottom: 2px;
}

.status-value {
    font-size: 1rem;
    font-weight: 600;
}

/* 用户信息区域 */
.user-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: white;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.user-details {
    text-align: left;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
}

.user-role {
    font-size: 0.75rem;
    opacity: 0.8;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
}

.action-btn {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    font-size: 0.875rem;
    cursor: pointer;
    position: relative;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    color: white;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

#unreadBadge {
    box-shadow: 0 0 5px rgba(255, 68, 68, 0.5);
}

/* 客服悬浮组件样式 */
.customer-service-float {
    position: fixed;
    bottom: 80px;
    right: 30px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.float-main {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    user-select: none;
}

.float-main:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5);
}

.float-main:active {
    transform: scale(0.95);
}

.float-icon {
    position: relative;
    margin-bottom: 2px;
}

.service-icon {
    font-size: 20px;
    color: #fff;
    font-style: normal;
}

.float-text {
    font-size: 10px;
    color: #fff;
    font-weight: 600;
    text-align: center;
}

.float-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff4444;
    color: #fff;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 16px;
    text-align: center;
    animation: pulse 2s infinite;
    box-shadow: 0 2px 8px rgba(255, 68, 68, 0.4);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 功能面板样式 */
.float-panel {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 280px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.3s ease-out;
    transform-origin: bottom right;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-title {
    font-size: 14px;
    font-weight: 600;
}

.panel-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s;
}

.panel-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.panel-content {
    max-height: 400px;
    overflow-y: auto;
}

.service-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background 0.2s;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    position: relative;
}

.service-item:last-child {
    border-bottom: none;
}

.service-item:hover {
    background: rgba(102, 126, 234, 0.05);
}

.item-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-right: 12px;
    flex-shrink: 0;
}

.chat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.manage-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.payment-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.rate-icon {
    background: linear-gradient(135deg, #fa709a, #fee140);
}

.withdraw-icon {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.recharge-icon {
    background: linear-gradient(135deg, #ffecd2, #fcb69f);
}

.item-info {
    flex: 1;
}

.item-title {
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
}

.item-desc {
    font-size: 11px;
    color: #7f8c8d;
}

.item-badge {
    background: #ff4444;
    color: #fff;
    border-radius: 8px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 16px;
    text-align: center;
}

/* 拖拽状态 */
.customer-service-float.dragging {
    transition: none;
}

.customer-service-float.dragging .float-main {
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
}

/* 响应式适配 */
@media (max-width: 768px) {
    .customer-service-float {
        bottom: 60px;
        right: 20px;
    }

    .float-main {
        width: 50px;
        height: 50px;
    }

    .service-icon {
        font-size: 18px;
    }

    .float-text {
        font-size: 9px;
    }

    .float-panel {
        width: 260px;
        bottom: 60px;
    }
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
    width: 4px;
}

.panel-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.panel-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
</body>
</html>
