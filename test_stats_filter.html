<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计卡片筛选功能测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card.clickable {
            cursor: pointer;
            user-select: none;
        }
        
        .stat-card.clickable:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card.active {
            border: 2px solid #4CAF50;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
            transform: translateY(-2px);
        }
        
        .stat-card.filtering {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .stat-card.filtering::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }
        
        .stat-icon.pending { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .stat-icon.approved { background: linear-gradient(135deg, #28a745, #1e7e34); }
        .stat-icon.rejected { background: linear-gradient(135deg, #dc3545, #c82333); }
        .stat-icon.total { background: linear-gradient(135deg, #17a2b8, #138496); }
        
        .stat-title {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #212529;
            margin-bottom: 8px;
        }
        
        .stat-change {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #6c757d;
        }
        
        .status-display {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-chart-bar"></i> 统计卡片筛选功能测试</h1>
        
        <div class="test-section">
            <h2 class="test-title">充值/提现统计卡片</h2>
            
            <div class="stats-grid">
                <div class="stat-card pending clickable" onclick="filterByStatus('0')" title="点击查看待处理订单">
                    <div class="stat-header">
                        <div class="stat-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3 class="stat-title">待处理</h3>
                    </div>
                    <div class="stat-value">15</div>
                    <div class="stat-change">
                        <i class="fas fa-search"></i>
                        <span>点击筛选</span>
                    </div>
                </div>

                <div class="stat-card approved clickable" onclick="filterByStatus('1')" title="点击查看已通过订单">
                    <div class="stat-header">
                        <div class="stat-icon approved">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3 class="stat-title">已通过</h3>
                    </div>
                    <div class="stat-value">128</div>
                    <div class="stat-change">
                        <i class="fas fa-search"></i>
                        <span>¥25,680</span>
                    </div>
                </div>

                <div class="stat-card rejected clickable" onclick="filterByStatus('2')" title="点击查看已拒绝订单">
                    <div class="stat-header">
                        <div class="stat-icon rejected">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <h3 class="stat-title">已拒绝</h3>
                    </div>
                    <div class="stat-value">8</div>
                    <div class="stat-change">
                        <i class="fas fa-search"></i>
                        <span>¥1,200</span>
                    </div>
                </div>

                <div class="stat-card total clickable" onclick="filterByStatus('all')" title="点击查看所有订单">
                    <div class="stat-header">
                        <div class="stat-icon total">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="stat-title">总计</h3>
                    </div>
                    <div class="stat-value">151</div>
                    <div class="stat-change">
                        <i class="fas fa-search"></i>
                        <span>¥26,880</span>
                    </div>
                </div>
            </div>
            
            <div class="status-display">
                <strong>当前筛选状态：</strong> <span id="currentStatus">全部订单</span>
            </div>
            
            <div>
                <button class="btn btn-primary" onclick="testFilter('0')">测试待处理筛选</button>
                <button class="btn btn-success" onclick="testFilter('1')">测试已通过筛选</button>
                <button class="btn btn-danger" onclick="testFilter('2')">测试已拒绝筛选</button>
                <button class="btn btn-info" onclick="testFilter('all')">测试全部筛选</button>
            </div>
            
            <div class="log" id="testLog">
                <strong>测试日志：</strong><br>
                页面已加载，等待测试...
            </div>
        </div>
    </div>

    <script>
        // 模拟状态选择器
        let currentFilterStatus = 'all';
        
        // 日志函数
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `<br>[${timestamp}] ${message}`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // 筛选函数
        function filterByStatus(status) {
            log(`开始筛选状态: ${status}`);
            
            // 移除所有卡片的激活状态和筛选状态
            $('.stat-card').removeClass('active filtering');
            
            // 添加筛选加载状态到被点击的卡片
            let targetCard;
            let statusText;
            
            if (status === 'all') {
                targetCard = $('.stat-card.total');
                statusText = '全部订单';
            } else if (status === '0') {
                targetCard = $('.stat-card.pending');
                statusText = '待处理订单';
            } else if (status === '1') {
                targetCard = $('.stat-card.approved');
                statusText = '已通过订单';
            } else if (status === '2') {
                targetCard = $('.stat-card.rejected');
                statusText = '已拒绝订单';
            }
            
            if (targetCard) {
                targetCard.addClass('filtering');
                log(`添加筛选状态到: ${statusText}`);
            }
            
            // 更新状态显示
            currentFilterStatus = status;
            document.getElementById('currentStatus').textContent = statusText;
            
            // 模拟搜索延迟
            setTimeout(() => {
                // 移除筛选状态，添加激活状态
                $('.stat-card').removeClass('filtering');
                if (targetCard) {
                    targetCard.addClass('active');
                    log(`激活卡片: ${statusText}`);
                }
                
                log(`筛选完成: ${statusText}`);
            }, 1000);
        }
        
        // 测试函数
        function testFilter(status) {
            log(`手动测试筛选: ${status}`);
            filterByStatus(status);
        }
        
        // 页面加载完成
        $(document).ready(function() {
            log('页面初始化完成');
            log('统计卡片筛选功能已就绪');
            
            // 默认激活总计卡片
            $('.stat-card.total').addClass('active');
            log('默认激活总计卡片');
        });
    </script>
</body>
</html>
