{+include file='headers.html'+}
<link href="/css/default/user.css?v=2024" rel="stylesheet" type="text/css">
<link href="/css/default/jquery-ui.css" rel="stylesheet" type="text/css">
<script language="javascript" src="/js/md5.js"></script>
<script type="text/javascript" src="../js/ui/jquery-ui.js"></script>
<script type="text/javascript" src="../js/ui/dialog-hi.js"></script>
<script type="text/javascript" src="../js/jquery.ui.datepicker-zh-CN.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style type="text/css">
/* 现代化卡片式样式 */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

body {
    background: #f5f7fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 页面头部 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
}

.page-header h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-header p {
    margin: 8px 0 0 0;
    opacity: 0.9;
    font-size: 16px;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--box-shadow);
    border: 1px solid #e9ecef;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-color, var(--primary-color));
}

.stat-card.pending::before { --card-color: var(--warning-color); }
.stat-card.approved::before { --card-color: var(--success-color); }
.stat-card.rejected::before { --card-color: var(--danger-color); }
.stat-card.total::before { --card-color: var(--info-color); }

/* 统计卡片激活状态 */
.stat-card.active {
    border: 2px solid #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    transform: translateY(-2px);
}

.stat-card.active .stat-title {
    color: #4CAF50;
    font-weight: 600;
}

.stat-card.active .stat-icon {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

/* 筛选加载状态 */
.stat-card.filtering {
    opacity: 0.7;
    pointer-events: none;
}

.stat-card.filtering::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 可点击卡片样式 */
.stat-card.clickable {
    cursor: pointer;
    user-select: none;
}

.stat-card.clickable .stat-change span {
    transition: color 0.3s ease;
}

.stat-card.clickable:hover .stat-change span {
    color: var(--primary-color);
    font-weight: 500;
}

/* 点击提示动画 */
@keyframes clickHint {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.stat-card.clickable:hover .stat-change i {
    animation: clickHint 1s ease-in-out infinite;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-icon.pending { background: var(--warning-color); }
.stat-icon.approved { background: var(--success-color); }
.stat-icon.rejected { background: var(--danger-color); }
.stat-icon.total { background: var(--info-color); }

.stat-title {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #212529;
    margin: 8px 0;
}

.stat-change {
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-change.positive { color: var(--success-color); }
.stat-change.negative { color: var(--danger-color); }
.stat-change.neutral { color: #6c757d; }

/* 支付凭证样式 */
.proof-container {
    text-align: center;
}

.proof-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid #e9ecef;
    transition: var(--transition);
    object-fit: cover;
}

.proof-thumbnail:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.proof-label {
    font-size: 10px;
    color: #6c757d;
    margin-top: 4px;
}

.no-proof {
    text-align: center;
    color: #adb5bd;
    font-size: 12px;
}

.no-proof i {
    display: block;
    font-size: 20px;
    margin-bottom: 4px;
}

/* 筛选和搜索区域 */
.filter-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 24px;
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
    border: 1px solid #e9ecef;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 0; /* 防止内容溢出 */
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
    white-space: nowrap;
}

.filter-group input,
.filter-group select {
    padding: 12px 16px;
    border: 1px solid #ced4da;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    min-height: 44px; /* 确保足够的高度 */
    width: 100%;
    box-sizing: border-box;
}

.filter-group select {
    background-color: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    line-height: 1.5;
}

/* 确保选择框选项的样式 */
.filter-group select option {
    padding: 8px 12px;
    background-color: white;
    color: #212529;
    font-size: 14px;
}

/* 特殊处理状态选择框 */
.filter-group select.status {
    min-width: 120px;
    max-width: 100%;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filter-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .filter-group input,
    .filter-group select {
        padding: 10px 12px;
        min-height: 40px;
        font-size: 16px; /* 防止iOS缩放 */
    }

    .filter-group select {
        background-size: 14px;
        padding-right: 35px;
    }
}

/* 修复iOS Safari的选择框样式 */
@supports (-webkit-touch-callout: none) {
    .filter-group select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    }
}

/* 确保在所有浏览器中选择框都有正确的高度 */
.filter-group select {
    vertical-align: middle;
}

/* 修复Firefox的选择框样式 */
@-moz-document url-prefix() {
    .filter-group select {
        padding-right: 30px;
        background-size: 12px;
    }
}

.filter-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-outline {
    background: transparent;
    border: 1px solid #ced4da;
    color: #495057;
}

.btn-outline:hover {
    background: var(--light-color);
    border-color: #adb5bd;
}

/* 数据表格卡片 */
.table-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid #e9ecef;
}

.table-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-title {
    font-size: 18px;
    font-weight: 600;
    color: #212529;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.table-actions {
    display: flex;
    gap: 12px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid #f1f3f4;
}

.data-table th {
    background: var(--light-color);
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tr:hover {
    background: var(--light-color);
}

.data-table td {
    font-size: 14px;
    color: #212529;
}

.main {
  max-width: 1400px;
  margin: 20px auto;
  padding: 0 15px;
}

.table-style {  
    width: 100%; /* 根据需要设置宽度 */  
    background-color: white; /* 设置表格背景颜色为白色 */  
    border-collapse: collapse; /* 可选，使表格边框更紧凑 */  
}  
  
/* 表格头样式 */ 
.data_table th, .data_table td {  
  
    border: 1px solid #ddd; /* 添加灰色边框 */  
     
}
.data_table th {  
    background-color: #f2f2f2; /* 添加表头背景色 */  
}  
  
/* 其他样式 */  
.td100 { width: 110px !important; }  
.tleft { text-align: left !important; padding-left: 3px; }  
.tright { text-align: right !important; padding-right: 3px; }  
.hide { display: none; }  
#dialog label { color: Red; }  
.statusbtn { width: 60px !important; }
/* 详情链接样式 */  
.xiangqing {  
    color: #007bff;  
    text-decoration: none;  
}  
  
.xiangqing:hover {  
    text-decoration: underline;  
}  
  
/* 隐藏类样式 */  
.hide {  
    display: none;  
}  
  
/* 特定样式优化 */  
.td100 {  
    width: 110px !important;  
}  
  
.tleft {  
    text-align: left !important;  
    padding-left: 3px;  
}  
  
.tright {  
    text-align: right !important;  
    padding-right: 3px;  
}  
  
#dialog label {  
    color: Red;  
}  
  
.statusbtn {  
    width: 60px !important;  
    height: 30px !important; /* 假设您想要的高度是30px */  
}
.xiangqing, .tongguo, .jujue {  
    padding: 5px 10px;  
    margin: 2px;  
    border-radius: 5px;  
    text-decoration: none;  
    color: #ffffff;  
}  
  
.xiangqing {  
    background-color: #007bff; /* 蓝色 */  
}  
  
.tongguo {  
    background-color: #28a745; /* 绿色 */  
}  
  
.jujue {
     background-color: #ff9900; /* 红色 */
}

/* 支付方式徽章样式 */
.payment-method-display {
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.payment-badge.bank {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border: 1px solid rgba(74, 144, 226, 0.3);
}

.payment-badge.alipay {
    background: linear-gradient(135deg, #1677ff, #0958d9);
    border: 1px solid rgba(22, 119, 255, 0.3);
}

.payment-badge.wechat {
    background: linear-gradient(135deg, #07c160, #05a050);
    border: 1px solid rgba(7, 193, 96, 0.3);
}

.payment-badge.usdt {
    background: linear-gradient(135deg, #f7931a, #e8851a);
    border: 1px solid rgba(247, 147, 26, 0.3);
}

.payment-badge.other {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.payment-badge i {
    font-size: 11px;
}

/* 支付方式徽章悬停效果 */
.payment-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 响应式支付方式显示 */
@media (max-width: 768px) {
    .payment-badge {
        font-size: 10px;
        padding: 3px 6px;
        gap: 2px;
    }

    .payment-badge i {
        font-size: 9px;
    }
}
</style>
</head>
<body style="overflow:hidden">
<script id=myjs language="javascript">var mulu='{+$mulu+}';var js=1;var sss='money_chongzhi';</script>



<div class="main">
		<!-- 页面头部 -->
		<div class="page-header">
			<div style="display: flex; justify-content: space-between; align-items: center;">
				<div>
					<h1><i class="fas fa-credit-card"></i> 充值管理</h1>
					<p>管理用户充值订单，查看统计数据和处理审核</p>
				</div>
				<div>
					<button type="button" class="btn btn-outline" onclick="refreshStatistics()" style="background: rgba(255,255,255,0.2); border-color: rgba(255,255,255,0.3); color: white;">
						<i class="fas fa-sync-alt"></i> 刷新统计
					</button>
				</div>
			</div>
		</div>

		<!-- 统计卡片 -->
		<div class="stats-grid" id="statsGrid">
			<div class="stat-card pending clickable" onclick="filterByStatus('0')" title="点击查看待处理订单">
				<div class="stat-header">
					<div class="stat-icon pending">
						<i class="fas fa-clock"></i>
					</div>
					<h3 class="stat-title">待处理</h3>
				</div>
				<div class="stat-value" id="pendingCount">-</div>
				<div class="stat-change neutral">
					<i class="fas fa-search"></i>
					<span>点击筛选</span>
				</div>
			</div>

			<div class="stat-card approved clickable" onclick="filterByStatus('1')" title="点击查看已通过订单">
				<div class="stat-header">
					<div class="stat-icon approved">
						<i class="fas fa-check-circle"></i>
					</div>
					<h3 class="stat-title">已通过</h3>
				</div>
				<div class="stat-value" id="approvedCount">-</div>
				<div class="stat-change positive">
					<i class="fas fa-search"></i>
					<span id="approvedAmount">¥0</span>
				</div>
			</div>

			<div class="stat-card rejected clickable" onclick="filterByStatus('2')" title="点击查看已拒绝订单">
				<div class="stat-header">
					<div class="stat-icon rejected">
						<i class="fas fa-times-circle"></i>
					</div>
					<h3 class="stat-title">已拒绝</h3>
				</div>
				<div class="stat-value" id="rejectedCount">-</div>
				<div class="stat-change negative">
					<i class="fas fa-search"></i>
					<span id="rejectedAmount">¥0</span>
				</div>
			</div>

			<div class="stat-card total clickable" onclick="filterByStatus('all')" title="点击查看所有订单">
				<div class="stat-header">
					<div class="stat-icon total">
						<i class="fas fa-chart-bar"></i>
					</div>
					<h3 class="stat-title">总计</h3>
				</div>
				<div class="stat-value" id="totalCount">-</div>
				<div class="stat-change neutral">
					<i class="fas fa-search"></i>
					<span id="totalAmount">¥0</span>
				</div>
			</div>
		</div>

		<!-- 筛选区域 -->
		<div class="filter-section">
			<div class="filter-grid">
				<div class="filter-group">
					<label>状态筛选</label>
					<select name="status" class="status" title="选择要筛选的订单状态">
						<option value="-1" selected="selected">全部状态</option>
						<option value="0" {+if $status=='0'+}selected{+/if+}>待处理</option>
						<option value="1" {+if $status=='1'+}selected{+/if+}>已通过</option>
						<option value="2" {+if $status=='2'+}selected{+/if+}>已拒绝</option>
					</select>
				</div>
				<div class="filter-group">
					<label>用户搜索</label>
					<input name="name" id="username" placeholder="输入用户名或ID">
				</div>
				<div class="filter-group">
					<label>开始日期</label>
					<input id="sdate" value="{+$sdate+}" placeholder="选择开始日期">
				</div>
				<div class="filter-group">
					<label>结束日期</label>
					<input id="edate" value="{+$edate+}" placeholder="选择结束日期">
				</div>
			</div>
			<div class="filter-actions">
				<button type="button" class="btn btn-primary query searchbtn">
					<i class="fas fa-search"></i> 查找
				</button>
				<button type="button" class="btn btn-outline" onclick="resetFilters()">
					<i class="fas fa-undo"></i> 重置
				</button>
				{+if $hide==1+}
				<button type="button" class="btn btn-outline del searchbtn">
					<i class="fas fa-trash"></i> 删除选中
				</button>
				{+/if+}
				<button type="button" class="btn btn-outline" onclick="exportData()">
					<i class="fas fa-download"></i> 导出数据
				</button>
			</div>
			<input type="hidden" name="fid" id="fid" value="" />
		</div>
		<!-- 数据表格 -->
		<div class="table-card">
			<div class="table-header">
				<h3 class="table-title">
					<i class="fas fa-list"></i> 充值订单列表
				</h3>
				<div class="table-actions">
					<button type="button" class="btn btn-outline" onclick="refreshData()">
						<i class="fas fa-sync-alt"></i> 刷新
					</button>
				</div>
			</div>
			<div class="table-responsive">
				<table class="data-table">
					<thead>
						<tr>
                            <th><input type="checkbox" class="all" /></th>
                            <th><i class="fas fa-calendar"></i> 提交时间</th>
                            <th><i class="fas fa-tag"></i> 类型</th>
							<th><i class="fas fa-user"></i> 用户信息</th>
                            <th><i class="fas fa-money-bill"></i> 金额</th>
                             <th><i class="fas fa-percentage"></i> 手续费</th>
                             <th><i class="fas fa-credit-card"></i> 支付方式</th>
                             <th><i class="fas fa-info-circle"></i> 收款信息</th>
                             <th><i class="fas fa-image"></i> 支付凭证</th>
                             <th><i class="fas fa-clock"></i> 入账时间</th>
                             <th><i class="fas fa-flag"></i> 状态</th>
                             <th><i class="fas fa-user-plus"></i> 提交人</th>
                             <th><i class="fas fa-user-check"></i> 受理人</th>
                             <th><i class="fas fa-calendar-check"></i> 受理时间</th>
                             <th><i class="fas fa-cogs"></i> 操作</th>
						</tr>
					</thead>
					<tbody>
                  {+section name=i loop=$marr+}
						<tr uid='{+$marr[i].userid+}' username='{+$marr[i].username+}'>
                        <td><input type="checkbox" value="{+$marr[i].id+}" /></td>
                        <td class="tjtime">{+$marr[i].tjtime+}</td>
                        <td class="mtype">{+$marr[i].mtype+}</td>
                        <td class="username">{+$marr[i].username+}({+$marr[i].tname+})</td>
                        <td class="money tleft">¥{+$marr[i].money+}</td>
                        <td class="sxfei tleft">¥{+$marr[i].sxfei+}</td>
                        <td class="fs">
                            <div class="payment-method-display">
                                {+if $marr[i].payment_method == 'bank'+}
                                    <span class="payment-badge bank">
                                        <i class="fas fa-university"></i> 银行转账
                                    </span>
                                {+elseif $marr[i].payment_method == 'alipay'+}
                                    <span class="payment-badge alipay">
                                        <i class="fab fa-alipay"></i> 支付宝
                                    </span>
                                {+elseif $marr[i].payment_method == 'wechat'+}
                                    <span class="payment-badge wechat">
                                        <i class="fab fa-weixin"></i> 微信支付
                                    </span>
                                {+elseif $marr[i].payment_method == 'usdt'+}
                                    <span class="payment-badge usdt">
                                        <i class="fab fa-bitcoin"></i> USDT
                                    </span>
                                {+else+}
                                    <span class="payment-badge other">
                                        <i class="fas fa-credit-card"></i> {+$marr[i].fs+}
                                    </span>
                                {+/if+}
                            </div>
                        </td>
                        <td class="bank">
                            {+if $marr[i].payment_method == 'bank'+}
                                {+$marr[i].bank+}<br>
                                <small>{+$marr[i].sname+} | {+$marr[i].snum+}</small>
                            {+elseif $marr[i].payment_method == 'alipay'+}
                                支付宝<br>
                                <small>{+$marr[i].alipay_name+} | {+$marr[i].alipay_account+}</small>
                            {+elseif $marr[i].payment_method == 'wechat'+}
                                微信支付<br>
                                <small>{+$marr[i].wechat_name+} | {+$marr[i].wechat_account+}</small>
                            {+elseif $marr[i].payment_method == 'usdt'+}
                                USDT<br>
                                <small>{+$marr[i].usdt_amount+} USDT | 汇率: {+$marr[i].exchange_rate+}</small>
                            {+else+}
                                {+$marr[i].bank+}({+$marr[i].sname+})
                            {+/if+}
                        </td>
                        <td class="proof">
                            {+if $marr[i].proof_image+}
                                <div class="proof-container">
                                    <img src="{+$marr[i].proof_image+}" class="proof-thumbnail" onclick="showProofImage('{+$marr[i].proof_image+}')" alt="支付凭证" onerror="this.src='/images/no-image.png'; this.onerror=null;">
                                    <div class="proof-label">支付凭证</div>
                                </div>
                            {+else+}
                                <div class="no-proof">
                                    <i class="fas fa-image"></i>
                                    <span>无凭证</span>
                                </div>
                            {+/if+}
                        </td>
                        <td class="time">{+$marr[i].cuntime+}</td>
                        <td class="status" v='{+$marr[i].status+}'>
                            <div class="status-display">
                                {+if $marr[i].ustatus == '0'+}
                                    <span class="status-badge pending" onclick="showStatusPanel(this, {+$marr[i].id+})" title="点击修改状态">
                                        <i class="fas fa-clock"></i> 待处理
                                    </span>
                                {+elseif $marr[i].ustatus == '1'+}
                                    <span class="status-badge approved">
                                        <i class="fas fa-check-circle"></i> 已通过
                                    </span>
                                {+elseif $marr[i].ustatus == '2'+}
                                    <span class="status-badge rejected">
                                        <i class="fas fa-times-circle"></i> 已拒绝
                                    </span>
                                {+else+}
                                    <span class="status-badge unknown">
                                        <i class="fas fa-question-circle"></i> 未知状态
                                    </span>
                                {+/if+}
                            </div>
                        </td>
                        <td class="tjname">{+$marr[i].tjname+}</td>                        
                        <td class="clname">{+$marr[i].clname+}</td>
                        <td class="cltime">{+$marr[i].cltime+}</td>
                       <td>
                           <div class="action-buttons">
                               <button type="button" class="action-btn view" onclick="showDetail({+$marr[i].id+})" title="查看详情">
                                   <i class="fas fa-eye"></i> 详情
                               </button>
                               <button type="button" class="action-btn view" onclick="showUserInfo({+$marr[i].userid+})" title="用户信息">
                                   <i class="fas fa-user"></i> 用户
                               </button>
                               {+if $marr[i].ustatus=='0'+}
                               <button type="button" class="action-btn approve" onclick="tg({+$marr[i].id+})" title="审核通过">
                                   <i class="fas fa-check"></i> 通过
                               </button>
                               <button type="button" class="action-btn reject" onclick="jj({+$marr[i].id+})" title="审核拒绝">
                                   <i class="fas fa-times"></i> 拒绝
                               </button>
                               {+/if+}
                           </div>
                       </td>
                        <td class="hide cuntime">{+$marr[i].cuntime+}</td>
                        <td class="hide snum">{+$marr[i].snum+}</td>
                        <td class="hide uname">{+$marr[i].uname+}</td>
                        <td class="hide unum">{+$marr[i].unum+}</td>
                        <td class="hide bz">{+$marr[i].bz+}</td>
                        <td class="hide ms">{+$marr[i].ms+}</td>
                        <td class="hide pass">{+$marr[i].pass+}</td>
                        <td class="hide mtype">{+$marr[i].mtype+}</td>
						</tr>
                  {+/section+}		           
					</tbody>
				</table>
                
				<div class="page"><div class="page_info" upage='{+$upage+}' pcount='{+$pcount+}' rcount='{+$rcount+}'>
<span class="record">共 <span>{+$rcount+}</span> 条记录</span>
<span class="page_count">共 <span>{+$pcount+}</span> 页</span>
<span class="page_control">

</span>
</div>
</div>
			</div>
		</div>
	</div>
    
    <!--<div id="statusPanel" class="popdiv" style="position:absolute; display: none;"><div class="title">修改订单状态<i></i></div><div class="statuslist"><label><input name="ustatus" type="radio" value="1">成功</label><label><input name="ustatus" type="radio" value="2">失败</label></div></div>-->
<script language="javascript">
// 页面加载时初始化
$(document).ready(function() {
    loadStatistics();
    initDatePickers();
    initActiveStatusCard();

    // 每30秒刷新一次统计数据
    setInterval(loadStatistics, 30000);
});

// 初始化激活状态卡片
function initActiveStatusCard() {
    const currentStatus = $('.status').val();
    console.log('当前状态:', currentStatus);

    // 移除所有激活状态
    $('.stat-card').removeClass('active filtering');

    // 根据当前状态激活对应卡片
    switch (currentStatus) {
        case '0':
            $('.stat-card.pending').addClass('active');
            console.log('激活待处理卡片');
            break;
        case '1':
            $('.stat-card.approved').addClass('active');
            console.log('激活已通过卡片');
            break;
        case '2':
            $('.stat-card.rejected').addClass('active');
            console.log('激活已拒绝卡片');
            break;
        case '-1':
        default:
            $('.stat-card.total').addClass('active');
            console.log('激活总计卡片');
            break;
    }
}

// 加载统计数据
function loadStatistics() {
    console.log('开始加载统计数据...'); // 调试信息

    $.get(mulu + 'money.php?xtype=statistics', function(res) {
        console.log('统计数据响应:', res); // 调试信息

        if (typeof res === 'string') {
            try {
                res = JSON.parse(res);
            } catch (e) {
                console.error('JSON解析失败:', e, res);
                return;
            }
        }

        if (res.code === 1) {
            console.log('统计数据:', res.data); // 调试信息
            updateStatistics(res.data);
        } else {
            console.error('统计数据返回错误:', res.msg || '未知错误');
            // 显示默认数据
            updateStatistics({
                pending_count: 0,
                approved_count: 0,
                rejected_count: 0,
                total_count: 0,
                approved_amount: 0,
                rejected_amount: 0,
                total_amount: 0
            });
        }
    }).fail(function(xhr, status, error) {
        console.error('统计数据加载失败:', status, error);
        console.error('响应内容:', xhr.responseText);

        // 显示默认数据
        updateStatistics({
            pending_count: 0,
            approved_count: 0,
            rejected_count: 0,
            total_count: 0,
            approved_amount: 0,
            rejected_amount: 0,
            total_amount: 0
        });
    });
}

// 更新统计显示
function updateStatistics(data) {
    console.log('更新统计显示:', data); // 调试信息

    // 数字动画效果
    function animateNumber(element, newValue) {
        const $element = $(element);
        const currentValue = parseInt($element.text()) || 0;

        if (currentValue !== newValue) {
            $element.prop('Counter', currentValue).animate({
                Counter: newValue
            }, {
                duration: 1000,
                easing: 'swing',
                step: function (now) {
                    $element.text(Math.ceil(now));
                }
            });
        }
    }

    // 金额动画效果
    function animateAmount(element, newValue, prefix = '¥') {
        const $element = $(element);
        const currentText = $element.text().replace(prefix, '');
        const currentValue = parseFloat(currentText) || 0;
        const targetValue = parseFloat(newValue) || 0;

        if (Math.abs(currentValue - targetValue) > 0.01) {
            $element.prop('Counter', currentValue).animate({
                Counter: targetValue
            }, {
                duration: 1000,
                easing: 'swing',
                step: function (now) {
                    $element.text(prefix + now.toFixed(2));
                }
            });
        }
    }

    // 更新数字
    animateNumber('#pendingCount', data.pending_count || 0);
    animateNumber('#approvedCount', data.approved_count || 0);
    animateNumber('#rejectedCount', data.rejected_count || 0);
    animateNumber('#totalCount', data.total_count || 0);

    // 更新金额
    animateAmount('#approvedAmount', data.approved_amount || 0);
    animateAmount('#rejectedAmount', data.rejected_amount || 0);
    animateAmount('#totalAmount', data.total_amount || 0);

    // 更新变化趋势（如果有变化数据）
    if (data.changes) {
        updateChangeIndicators(data.changes);
    }

    // 添加最后更新时间
    if (data.update_time) {
        $('.stats-grid').attr('title', '最后更新: ' + data.update_time);
    }
}

// 更新变化指示器
function updateChangeIndicators(changes) {
    // 这里可以添加变化趋势的显示逻辑
    console.log('变化数据:', changes);
}

// 初始化日期选择器
function initDatePickers() {
    $('#sdate, #edate').datepicker({
        dateFormat: 'yy-mm-dd',
        changeMonth: true,
        changeYear: true,
        maxDate: new Date()
    });
}

// 重置筛选条件
function resetFilters() {
    $('.status').val('-1');
    $('#username').val('');
    $('#sdate').val('');
    $('#edate').val('');
}

// 刷新数据
function refreshData() {
    console.log('刷新数据...');

    // 显示加载状态
    $('.stats-grid .stat-value').text('加载中...');

    // 重新加载统计数据
    loadStatistics();

    // 刷新页面数据
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// 手动刷新统计数据（不刷新整个页面）
function refreshStatistics() {
    console.log('刷新统计数据...');

    // 显示加载状态
    $('.stats-grid .stat-value').text('...');

    // 重新加载统计数据
    loadStatistics();
}

// 根据状态筛选订单
function filterByStatus(status) {
    console.log('筛选状态:', status);

    // 移除所有卡片的激活状态和筛选状态
    $('.stat-card').removeClass('active filtering');

    // 添加筛选加载状态到被点击的卡片
    let targetCard;
    if (status === 'all') {
        targetCard = $('.stat-card.total');
    } else if (status === '0') {
        targetCard = $('.stat-card.pending');
    } else if (status === '1') {
        targetCard = $('.stat-card.approved');
    } else if (status === '2') {
        targetCard = $('.stat-card.rejected');
    }

    if (targetCard) {
        targetCard.addClass('filtering');
    }

    // 设置筛选条件
    if (status === 'all') {
        $('.status').val('-1');
    } else {
        $('.status').val(status);
    }

    // 清空其他筛选条件
    $('#username').val('');
    $('#sdate').val('');
    $('#edate').val('');

    // 显示加载提示
    showFilteringMessage(status);

    // 执行搜索 - 直接调用get函数而不是模拟点击
    setTimeout(() => {
        // 移除筛选状态，添加激活状态
        $('.stat-card').removeClass('filtering');
        if (targetCard) {
            targetCard.addClass('active');
        }

        if (typeof get === 'function') {
            get();
        } else {
            // 如果get函数不存在，则模拟点击搜索按钮
            $('.query.searchbtn').first().trigger('click');
        }
    }, 500);
}

// 显示筛选提示消息
function showFilteringMessage(status) {
    let message = '';
    switch (status) {
        case '0':
            message = '正在加载待处理订单...';
            break;
        case '1':
            message = '正在加载已通过订单...';
            break;
        case '2':
            message = '正在加载已拒绝订单...';
            break;
        case 'all':
            message = '正在加载所有订单...';
            break;
        default:
            message = '正在加载订单数据...';
    }

    // 创建临时提示
    const $toast = $('<div class="filter-toast">' + message + '</div>');
    $toast.css({
        position: 'fixed',
        top: '20px',
        right: '20px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '10px 20px',
        borderRadius: '5px',
        zIndex: 9999,
        fontSize: '14px'
    });

    $('body').append($toast);

    // 3秒后自动移除
    setTimeout(() => {
        $toast.fadeOut(300, function() {
            $(this).remove();
        });
    }, 3000);
}

// 导出数据
function exportData() {
    var params = {
        status: $('.status').val(),
        username: $('#username').val(),
        sdate: $('#sdate').val(),
        edate: $('#edate').val()
    };

    var queryString = $.param(params);
    window.open(mulu + 'money.php?xtype=export&' + queryString, '_blank');
}

function tg(e){
    	$.ajax({
			type: 'POST',
			url: mulu + 'money.php',
			cache: false,
			data: 'xtype=upczstatus&ids=' + e + "&status=1",
			success: function(m) {
				if (Number(m) == 1) {
                   //get();
                  alert("通过成功!"); 
                  location.reload();
				}else if (Number(m) == 2) {
                    alert("拒绝成功");
                    location.reload();
				}else if (Number(m) == 10) {
                    alert("用户还有未结算注单!");
				}else if (Number(m) == 20) {
                    alert("用户余额不足，无法操作!");
				}
			}
		})
}

function jj(e){
    	$.ajax({
			type: 'POST',
			url: mulu + 'money.php',
			cache: false,
			data: 'xtype=upczstatus&ids=' + e + "&status=2",
			success: function(m) {
				if (Number(m) == 1) {
                   //get();
                  alert("通过成功!"); 
                  location.reload();
				}else if (Number(m) == 2) {
                    alert("拒绝成功");
                    location.reload();
				}else if (Number(m) == 10) {
                    alert("用户还有未结算注单!");
				}else if (Number(m) == 20) {
                    alert("用户余额不足，无法操作!");
				}
			}
		})
}

// 显示支付凭证大图
function showProofImage(imageSrc) {
	if (!imageSrc) {
		alert('无支付凭证');
		return;
	}

	var modal = $('<div class="image-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 9999; display: flex; align-items: center; justify-content: center;">' +
		'<div style="position: relative; max-width: 90%; max-height: 90%;">' +
		'<img src="' + imageSrc + '" style="max-width: 100%; max-height: 100%; border-radius: 8px;">' +
		'<button onclick="$(this).closest(\'.image-modal\').remove()" style="position: absolute; top: -10px; right: -10px; background: #fff; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 18px;">×</button>' +
		'</div>' +
		'</div>');

	$('body').append(modal);

	modal.click(function(e) {
		if (e.target === this) {
			$(this).remove();
		}
	});
}

// 显示详情
function showDetail(id) {
	$.get(mulu + 'money.php?xtype=detail&id=' + id, function(res) {
		if (typeof res === 'string') {
			res = JSON.parse(res);
		}
		if (res.code === 1) {
			var data = res.data;
			var content = '<div style="padding: 20px; line-height: 1.6;">' +
				'<h3>充值详情</h3>' +
				'<div><strong>订单号：</strong>' + data.orderid + '</div>' +
				'<div><strong>用户：</strong>' + data.username + ' (ID: ' + data.userid + ')</div>' +
				'<div><strong>充值金额：</strong>¥' + parseFloat(data.money).toFixed(2) + '</div>' +
				'<div><strong>手续费：</strong>¥' + parseFloat(data.sxfei || 0).toFixed(2) + '</div>' +
				'<div><strong>实际金额：</strong>¥' + parseFloat(data.actual_amount || data.money).toFixed(2) + '</div>' +
				'<div><strong>支付方式：</strong>' + getPaymentMethodText(data.payment_method) + '</div>';

			if (data.payment_method === 'usdt') {
				content += '<div><strong>USDT数量：</strong>' + data.usdt_amount + ' USDT</div>' +
					'<div><strong>汇率：</strong>1 USDT = ' + data.exchange_rate + ' CNY</div>' +
					'<div><strong>钱包地址：</strong>' + data.usdt_address + '</div>' +
					'<div><strong>网络：</strong>' + data.usdt_network + '</div>';
			}

			if (data.proof_image) {
				content += '<div><strong>支付凭证：</strong><br><img src="' + data.proof_image + '" style="max-width: 200px; cursor: pointer;" onclick="showProofImage(\'' + data.proof_image + '\')"></div>';
			}

			content += '<div><strong>提交时间：</strong>' + data.tjtime + '</div>' +
				'<div><strong>状态：</strong>' + getStatusText(data.status) + '</div>';

			if (data.admin_note) {
				content += '<div><strong>管理员备注：</strong>' + data.admin_note + '</div>';
			}

			content += '</div>';

			showModal('充值详情', content);
		} else {
			alert(res.msg || '获取详情失败');
		}
	}).fail(function() {
		alert('网络错误');
	});
}

// 显示用户绑定信息
function showUserInfo(userid) {
	$.get(mulu + 'money.php?xtype=user_info&userid=' + userid, function(res) {
		if (typeof res === 'string') {
			res = JSON.parse(res);
		}
		if (res.code === 1) {
			var data = res.data;
			var content = '<div style="padding: 20px; line-height: 1.6;">' +
				'<h3>用户绑定信息</h3>' +
				'<div><strong>用户名：</strong>' + data.username + '</div>' +
				'<div><strong>用户ID：</strong>' + data.userid + '</div>' +
				'<div><strong>当前余额：</strong>¥' + parseFloat(data.kmoney || 0).toFixed(2) + '</div>' +
				'<div><strong>注册时间：</strong>' + (data.regtime || '未知') + '</div>';

			// 银行卡信息
			if (data.bank_info && data.bank_info.length > 0) {
				content += '<h4 style="margin-top: 20px;">银行卡信息：</h4>';
				data.bank_info.forEach(function(bank) {
					content += '<div style="background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px;">' +
						'<div>银行：' + bank.bank + '</div>' +
						'<div>账号：' + bank.account + '</div>' +
						'<div>姓名：' + bank.name + '</div>' +
						(bank.branch ? '<div>开户行：' + bank.branch + '</div>' : '') +
						'</div>';
				});
			}

			// 支付宝信息
			if (data.alipay_info && data.alipay_info.length > 0) {
				content += '<h4 style="margin-top: 20px;">支付宝信息：</h4>';
				data.alipay_info.forEach(function(alipay) {
					content += '<div style="background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px;">' +
						'<div>账号：' + alipay.account + '</div>' +
						'<div>姓名：' + alipay.name + '</div>' +
						'</div>';
				});
			}

			// 微信信息
			if (data.wechat_info && data.wechat_info.length > 0) {
				content += '<h4 style="margin-top: 20px;">微信信息：</h4>';
				data.wechat_info.forEach(function(wechat) {
					content += '<div style="background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px;">' +
						'<div>账号：' + wechat.account + '</div>' +
						'<div>姓名：' + wechat.name + '</div>' +
						'</div>';
				});
			}

			// USDT信息
			if (data.usdt_info && data.usdt_info.length > 0) {
				content += '<h4 style="margin-top: 20px;">USDT信息：</h4>';
				data.usdt_info.forEach(function(usdt) {
					content += '<div style="background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px;">' +
						'<div>地址：' + usdt.address + '</div>' +
						'<div>网络：' + usdt.network + '</div>' +
						'</div>';
				});
			}

			content += '</div>';

			showModal('用户绑定信息', content);
		} else {
			alert(res.msg || '获取用户信息失败');
		}
	}).fail(function() {
		alert('网络错误');
	});
}

// 显示模态框
function showModal(title, content) {
	var modal = $('<div class="custom-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">' +
		'<div style="background: #fff; border-radius: 8px; max-width: 600px; max-height: 80%; overflow-y: auto; position: relative;">' +
		'<div style="padding: 15px; border-bottom: 1px solid #eee; font-weight: bold;">' + title +
		'<button onclick="$(this).closest(\'.custom-modal\').remove()" style="float: right; background: none; border: none; font-size: 20px; cursor: pointer;">×</button></div>' +
		'<div>' + content + '</div>' +
		'</div>' +
		'</div>');

	$('body').append(modal);

	modal.click(function(e) {
		if (e.target === this) {
			$(this).remove();
		}
	});
}

// 获取支付方式文本
function getPaymentMethodText(method) {
	var methods = {
		'bank': '银行转账',
		'alipay': '支付宝',
		'wechat': '微信支付',
		'usdt': 'USDT'
	};
	return methods[method] || method;
}

// 获取状态文本
function getStatusText(status) {
	var statusMap = {
		0: '待处理',
		1: '已通过',
		2: '已拒绝'
	};
	return statusMap[status] || '未知';
}

</script>

<!-- 引入充值管理相关的JavaScript文件 -->
<script language="javascript" src="/js/default/jshide/money_chongzhimyadmin.js"></script>
<script language="javascript">
// 确保页面加载完成后初始化
$(document).ready(function() {
    // 如果存在myready函数，则调用它
    if (typeof myready === 'function') {
        myready();
    }
});
</script>

</body></html>